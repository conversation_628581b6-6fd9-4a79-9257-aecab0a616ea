import { getNameOfMoments } from '@/utils/cross'
import { getPlatform } from '@/utils/utils'
// import { useWindowSize } from '@vueuse/core'
export const isXiaoin = false
// import { StarloveConstants } from './starloveConstants'

// export const NODE_ENV = process.env.NODE_ENV // 'production' 环境变量

// const INSCHOOL_APP_ID = 'wx7fbcea1395efbb63' //小程序 InShool appId
// const INSCHOOL_MP_ID = 'wx6c34e95e28f7f3b0' //公众号 InShool appId
const XIAOIN_MP_ID = 'wxab6895d97cd449c8' //公众号 行业圈 appId  万能小in（wxab6895d97cd449c8） 万能小in AI写作助手（wxd7025dc360a74a5d）
// const XIAOIN_WEB_ID = 'wx946384b6078be005' //公众号 行业圈 appId
export const XIAO_WN_ID = 'wxd7025dc360a74a5d' // 公众号 AI写作助手  xiaoin.cn
export const getAppId = () => {
  // console.log('getAppId')
  // if (isH5() && isWechatBrowser()) {
  //   return isXioin ? XIAO_WN_ID : XIAOIN_MP_ID
  // }
  // if (!isMobileDevice() && !isWechatBrowser()) {
  //   return isXioin ? XIAO_WN_ID : XIAOIN_MP_ID
  // }
  return isXiaoin ? XIAO_WN_ID : XIAOIN_MP_ID
}
// export const isH5 = () => {
//   return process.env.TARO_ENV === 'h5'
// }
// export const isWeapp = () => {
//   return process.env.TARO_ENV === 'weapp'
// }

export const isWechatBrowser = () => {
  return false
  // const ua = navigator.userAgent.toLowerCase();
  // if (ua.match(/MicroMessenger/i)) {
  //   //ios的ua中无miniProgram，但都有MicroMessenger（表示是微信浏览器）
  //   wx.miniProgram.getEnv((res) => {});
  //   return true;
  // } else {
  //   // console.log('不在微信里')
  //   return false;
  // }
}

// export const isMobileDevice = () => {
//   const userAgent = navigator.userAgent
//   return /Android|webOS|iPhone|BlackBerry|IEMobile|Opera Mini/i.test(userAgent)
// }
export const isMobileDevice = () => {
  return false
  // const userAgent = navigator.userAgent;
  // return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
  //   userAgent
  // );
}

export const isiPadDevice = () => {
  return false
  // const userAgent = navigator.userAgent;
  // return /iPad|iPod/i.test(userAgent);
}

// export const isMac = /macintosh|mac os x/i.test(navigator.userAgent); //苹果

// export const isWindows = /windows|win32/i.test(navigator.userAgent); //Windows

// export const isPcWeb = () => {
//   // const { width } = useWindowSize()
//   const width = document.documentElement.clientWidth
//   // console.log('isiPadDevice ==>', isiPadDevice())
//   return process.env.TARO_ENV === 'h5' && width > 960
//   // return process.env.TARO_ENV === 'h5' && (isiPadDevice() || width.value > 960)
// }

export const getVodSubAppId = () => {
  if (process.env.NODE_ENV === 'production') {
    return '1500007542'
  }
  return '1500007541'
}
/** 首页导航 */
export enum NAV_BAR {
  /** 首页 */
  HOME = 1,
  /**广场 */
  SQUARE = 2,
  /**发布 */
  COMPOSER = 3,
  /**消息 */
  MESSAGES = 4,
  /**我的 */
  MINE = 5,
}
export const HTTP_STATUS = {
  SUCCESS: 200,
  CREATED: 201,
  ACCEPTED: 202,
  CLIENT_ERROR: 400,
  AUTHENTICATE: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
  GATEWAY_TIMEOUT: 504,
  SHORTAGE_OF_COINS: 7001,
  FAILED_PHONE_BINDING: 1001,
  FAILED_QRCODE_INVALID: 1002, //web扫码登录时二维码失效
  FAILED_SUBMIT_AGAIN: 20001, //接口重复提交
  MOBILE_NOT_BOUND: 7002, //手机号未绑定
}

export enum MsgInboxType {
  system = 1,
  order = 2,
  lifecircle = 3,
  notice = 4,
  comment = 5,
  commentReply = 6,
  like = 7,
  collect = 8,
  at = 9,
  attentionMoment = 10, // 关注的人发的动态-我的关注
  attention = 11, // 关注事件
  attentionOverseer = 12, // 关注动态-我的监督
  attentionLooked = 13, // 关注事件-我的围观
  gift = 14,
  impressionTag = 15, //印象标签
  momentApplyForSort = 22, // 置顶消息
  mutualAll = 0, //全部互动
}

///消息分类 ---关注消息列表
export const lifecircleMessageList: MsgInboxType[] = [
  MsgInboxType.lifecircle,
  MsgInboxType.attentionMoment,
  MsgInboxType.attentionLooked,
  MsgInboxType.attentionOverseer,
]
///消息分类 ---互动消息列表
export const interactiveMessageList: MsgInboxType[] = [
  MsgInboxType.comment,
  MsgInboxType.commentReply,
  MsgInboxType.at,
  MsgInboxType.like,
  MsgInboxType.collect,
  MsgInboxType.attention,
  MsgInboxType.gift,
  MsgInboxType.impressionTag,
]

///消息分类 ---系统消息列表
export const systemMessageList: MsgInboxType[] = [
  MsgInboxType.system,
  MsgInboxType.notice,
  MsgInboxType.momentApplyForSort,
]
export enum MessageStatus {
  unread = 1,
  readed = 2,
}
export enum SimpleRouteType {
  order = 1,
  article = 2,
  articleComment = 3,
  moment = 4,
  momentComment = 5,
  topic = 6,
  userInfo = 7,
  momentsDailyHot = 8,
  lifecircle = 10,
  myLifecircleList = 11, //学生认证审核消息
  checkin = 12,
  challenge = 13,
  momentApplyForSort = 22,
  giftOne = 53, //礼物的数字有两个14和53
  giftTwo = 14,
  mine = 15,
  imMessageIndex = 16,
  hiIndex = 17,
  imChat = 40,
  hiChat = 45,
  msgBoxComment = 41,
  msgBoxLike = 42,
  msgBoxAttention = 43,
  msgBoxSysten = 44,
  starsRecord = 51,
  flagList = 66,
  challengeList = 65,
  fansList = 52,
  reportList = 61,
  reportDetail = 62,
  verifyList = 63,
  verifyDetail = 64,
  unRegister = 91,
  feedback = 92,
  requestSchool = 93,
  starOrder = 100,
  starSign = 101,
  shopOrder = 200,
  shopSign = 101,
  shop = 300,
  goods = 400,
  h5 = 500,

  adminSystemMsg = 999, //通过admin后台发送的系统消息，且落地页为空
}

export const cosBucketPrefixStellar = 'https://stellar-**********.cos.ap-shanghai.myqcloud.com/'

export const cosBucketPrefixInSchool = 'https://inschool-1255701666.cos.ap-shanghai.myqcloud.com/'

export const cosBucketPrefixInBusiness = 'https://business-1255701666.cos.ap-shanghai.myqcloud.com/'

export const cosBucketPrefixStatic = 'https://static-**********.file.myqcloud.com/'

export const userSignedInIcon = `${cosBucketPrefixStatic}mini-app/inschool/sign_in.png` //签到
export const userNotSignInIcon = `${cosBucketPrefixStatic}mini-app/inschool/nosign_in.png` //未签到

export const logoUrl = `${cosBucketPrefixStatic}/nuxt/images/logo.png`

// business-**********.cos.ap-shanghai.myqcloud.com
// inschool-1255701666
// business-1255701666.cos.ap-shanghai.myqcloud.com

export const slogan = '标记我的大学'
export const lifecircleLogoSquare = `${cosBucketPrefixStatic}life-circle/logox152-square.png`
export const lifecircleLogoXingxiai = `${cosBucketPrefixStatic}xingxiai/logo-xingxiai.png!x100`

export const inviteFriendDefaultBackgroundImg = `${cosBucketPrefixStatic}life-circle/share-invitation-default-3.0.png`

export const cosIconUrlPrefix = `${cosBucketPrefixStatic}prod/0/image/`

export const hotRankIcon = `${cosBucketPrefixStatic}life-circle/icon/today_hot_rank.png`

export const loginPoster = `${cosBucketPrefixStatic}life-circle/login-icon-v3.0.png`

export const xingxiaiBusinessCosIconUrlPrefix = `${cosBucketPrefixStatic}mini-app/`

export const xiaoinRechargePageTopBg = `${cosBucketPrefixStatic}mini-app/recharge-top-bg.png`

export const defaultAvatar = 'https://static-**********.file.myqcloud.com/nuxt/images/avatar.png'
export const defaultTeamAvatar = 'https://static-**********.file.myqcloud.com/nuxt/images/team_avatar.png'

export const contractQrcode = `${cosBucketPrefixStatic}mini-app/partnership-qrcode.png`

// // 至尊会员客服
export const ExclusiveMembership =
  'https://static-**********.file.myqcloud.com/xiaoin-h5/image/zhizun_server.png'

// // 黑利的企微二维码
export const wxqrcodeOfHeiLi =
  'https://static-**********.file.myqcloud.com/mini-app/roles/kefu240319.png'

export const wxqrcodeOfXiaoinCOMCN =
  'https://static-**********.file.myqcloud.com/xiaoin-h5/image/service_xiaoin_cn.png'

// // 老李的企微二维码
export const wxqrcodeOfLaoli =
  'https://static-**********.file.myqcloud.com/mini-app/roles/laoli-wxcode.png'

// 万能小in公众号二维码
export const wechatOfficialAccountsQrcode = isXiaoin
  ? 'https://static-**********.file.myqcloud.com/xiaoin-h5/image/official_accounts_xiao_cn1.jpg'
  : 'https://static-**********.file.myqcloud.com/xiaoin-h5/image/official_accounts1.jpg'

// 万能小in小程序码
export const wechatMiniappQrcode =
  'https://static-**********.file.myqcloud.com/h5/image/miniapp-qrcode.png'

export const createMagicAvatarExampleMaleImage =
  'https://static-**********.file.myqcloud.com/xiaoin-h5/image/example-male.png'
export const createMagicAvatarExampleFemaleImage =
  'https://static-**********.file.myqcloud.com/xiaoin-h5/image/example-female.png'

export const inviteMiniappBg = `https://static-**********.file.myqcloud.com/mini-app/invite-bg.png`
export const xiaoinLogo =
  'https://static-**********.file.myqcloud.com/mini-app/roles/roles_logo.png'

export const xiaoinSloganLogo =
  'https://static-**********.file.myqcloud.com/xiaoin-h5/bg/web-logo.png?v=1'

export const iconLinkPrefix = 'https://static-**********.file.myqcloud.com/xiaoin-h5/icons/new-pc/'

export const phoneRegExp = /^(?:(?:\+|00)86)?1\d{10}$/
export const IDCardRegExp =
  /(^\d{8}(0\d|10|11|12)([0-2]\d|30|31)\d{3}$)|(^\d{6}(18|19|20)\d{2}(0[1-9]|10|11|12)([0-2]\d|30|31)\d{3}(\d|X|x)$)/
// 网址(URL)正则校验
export const urlRegExp =
  /^(((ht|f)tps?):\/\/)?([^!@#$%^&*?.\s-]([^!@#$%^&*?.\s]{0,63}[^!@#$%^&*?.\s])?\.)+[a-z]{2,6}\/?/
export const markdownRegex = /(^#{1,6}\s)|(^-|\*\s)|(\*\*|__)|(\[.*?\]\(.*?\))/gm
//微信开放平台绑定的移动应用的AppId
// export const wechatAppAppIdCJ = 'wxa9d33003c484e17c' //'wx7fbcea1395efbb63'
export const wechatAppAppIdCJ = 'wxe2b2751fd670b97c'
export const wechatMiniAppAppId = wechatAppAppIdCJ //process.env.VUE_APP_MINIAPP_ID || 'wx7fbcea1395efbb63' //inschool小程序
export const clipPrefix = '#sl~'

export const lcIdOfSquare = '100'

export const lackOfBalanceErrorCode = 7001

export const anonymityUserId = '9999'

export const vipOrderStatus = '7'

export enum AdvertisingPosition {
  lifecircleNew = '1',
  lifecircleRecommend = '2',
  squareNew = '3',
  squareRecommend = '4',
  squareBanner = '5',
  lifecircleBanner = '6',
  momentsDetailBeforeComments = '7',
  momentsDetailAfterComments = '8',
}

// export enum AdvertisingType {
//   BANNER = 1,
//   FEED = 2
// }

export enum AdvertisingType {
  advertising = 1,
  activity = 2,
}

export enum AdvertisingPlatform {
  miniApp = '3',
  androidMiniApp = '4',
  iosMiniAapp = '5',
}

export enum LifecircleType {
  lifecircle = 1,
  school = 2,
  business = 3,
}

// LIFECIRCLE_EDIT("设置圈子"),
// MOMENTS_SORT("动态置顶"),
// TOPIC_SORT("话题置顶"),
export enum managePermissions {
  LIFECIRCLE_EDIT = 'LIFECIRCLE_EDIT',
  MOMENTS_SORT = 'MOMENTS_SORT',
  TOPIC_SORT = 'TOPIC_SORT',
}
export const managePermissionsList = [
  // { name: '设置圈子', value: managePermissions.LIFECIRCLE_EDIT },
  { name: `${getNameOfMoments()}置顶`, value: managePermissions.MOMENTS_SORT },
  { name: '话题置顶', value: managePermissions.TOPIC_SORT },
]
//账户类型
export enum accountType {
  LIFECIRCLE_MASTER = '3',
  ADVERTISER = '2',
}

export enum scopeType {
  ALL_SCHOOL = 1, //所有学校
  SINGLE_SCHOOL = 2, //单个学校
}

// 企业类型
export enum companyType {
  SUBJECT_TYPE_ENTERPRISE = 'SUBJECT_TYPE_ENTERPRISE', //企业
  SUBJECT_TYPE_MICRO = 'SUBJECT_TYPE_MICRO', //小微
}
export enum adPutOrderStatus {
  PENDING_PAYMENT = 1, //待支付
  WAITING_CHECK = 2, //审核中
  CHECK_SUCCESS = 3, //审核成功, 待投放
  CHECK_FAULT = 4, //审核失败
  PUT = 5, //投放中
  DONE = 6, //投放完成
}

export enum certVerifyType {
  noNeed = 0, //无需认证
  artificial = 1, //人工认证
  answer = 2, //答题认证
  auto = 3, //自动认证
}

export enum TopicTagType {
  none = 0, //普通话题
  pinnedTopic = 1, //置顶话题
  hotTopic = 2, //热门话题
  newTopic = 3, //最新话题
}

export enum MysteryBoxType {
  put = 1, //
  pick = 2, //
}

export enum MatchSchoolType {
  unlimited = 1, // 不限
  ourSchool = 2, // 本校
  designatedSchool = 3, //指定学校
}

export enum MomentsType {
  article = 1, //文章
  moments = 2, //动态
}

export enum CommentType {
  comment = 1, //评论
  reply = 2, //回复
}

export enum Gender {
  unlimited = 0, //
  male = 1, //
  female = 2,
}

export enum OpenUserLabelPageType {
  mine = 0, //编辑资料打开
  mysterybox = 1, //小纸条打开
}

export enum MediaType {
  video = 1, //
  audio = 2, //
}

export enum ShareType {
  appMessage, //微信消息
  timeline, //朋友圈
  photoSharing, //图片分享
  savePhotos, //保存图片
  report, //举报
  applyPin,
}
export enum MomentsShareType {
  momentsShare = 1,
  normalShare = 2,
  inviteShare = 3, //邀请同学
}

// //类型 1.文章,2.文章评论,3.畅聊,4.畅聊评论
export enum LikeContentType {
  article = '1',
  articleComment = '2',
  moment = '3',
  momentComment = '4',
}

export enum ApplyForSortType {
  circle = 10, //10.圈子
  topic = 6, //6.话题
  section = 19, //19.分区
}

// 打开编辑资料页面，还是打开申请加圈页面
export enum OpenEditUserType {
  isInschoolJoinCircle = '1',
}

// 邀请同学得分享，点击立即邀请跳转到首页，并弹出分享图片
export enum InviteFriendType {
  inviteFriend = '1',
}

export const defaultLifecircleNotice = '本校专属圈子，服务于全校同学的信息交流互助和社交需求'

export const pagePathOfDetail = '/pages/moment/detail/detail'

export const urlOfCustomerServiceByWechatWork =
  'https://work.weixin.qq.com/kfid/kfc19f78f7424212f6e'
export const corpIdOfCustomerServiceByWechatWork = 'ww1344d27f26e9aac3'

export const topupVipCanSeeContactWayHint = '查看联系方式仅对VIP用户开放使用'
export const topupVipCanAnonymityCommentAndSeeMomentHint = '匿名发布服务仅对VIP用户开放使用'
export const topupVipCanGroupPushHint = '群推送服务仅对VIP用户开放使用'
export const topupVipCanSeeMomentContentHint = 'VIP专享内容仅对VIP用户开放使用'

export const contactSearchCityOptionAllTextHint = '全部'

export const teamRechargeContactQueryCode = 'team-xiaoin' //加载team充值联系人二维码

export const teamVipPlanMaxMemberCount = 1000 //团队版充值最大成员数

export enum EditProfileActionType {
  avatar = 1,
  nickname = 2,
}

// 写作的大纲润色，ppt默认生成。接口默认值时ppt
export enum CreateOutlineCategory {
  wordOutline1 = 'word-outline-1', //一级大纲
  wordOutline2 = 'word-outline-2', //二级大纲
  wordOutline3 = 'word-outline-3', //三级大纲
  pptOutline = 'ppt-outline', //ppt大纲
}

export enum PresetTagType {
  common = 1,
  industry = 2,
  impression = 3,
}

export enum UserFieldsType {
  identity = '身份',
  experience = '经验',
  companyName = '企业名称',
  myResources = '我的资源',
  myRequirements = '我的需求',
}

export enum businessCardLocation {
  wrap = '1',
  row = '2',
}

export enum BookUploadFileType {
  bookTemplate = 1, //单章的临时上传的文件
  bookDataBase = 2 //上传到资料库
}

export enum ContactType {
  partner = 1, //'申请合伙人',
  service = 2, //'客服'
  custom = 3, //自定义人设
  exclusiveMembership = 4,
}

export enum RatingType {
  like = '2', //点赞
  dislike = '1', //取消点赞
}

export enum SubmissionStatus {
  init = 'init',
  needPay = 'need_pay',
  payed = 'payed',
  ing = 'ing',
  done = 'done',
  error = 'error',
  wait = 'wait',
}

export enum SubmissionCreatePPTStage {
  creatingOutline = 'creating_outline', //
  creatingPages = 'creating_pages', //
  completing = 'completing', //生成中
  completed = 'completed', //完成
  error = 'error',
}

export enum SubmissionCreatePaperStage {
  creatingOutline = 'creating_outline', //
  creatingPages = 'creating_chapters', //
  completing = 'completing', //生成中
  completed = 'completed', //完成
  error = 'error',
}

export enum SubmissionCreateMagicAvatarStage {
  creatingOutline = 'creating_outline', //
  creatingPages = 'creating_chapters', //
  completing = 'completing', //生成中
  completed = 'completed', //完成
  error = 'error',
}

export enum CreateContentSize {
  tiny = 'tiny',
  small = 'small',
  middle = 'middle',
  large = 'large',
}

export enum CreateContentLanguage {
  EN = 'en',
  CN = 'cn',
}

export enum CreateSubmissionAssistType {
  ai = 'ai',
  custom = 'custom',
  document = 'document',
}

export enum CreateSubmissionOutlineModalType {
  no = '无大纲，帮我AI生成',
  yes = '有大纲，需保持原文',
}

export enum CreateSubmissionAssistChineseType {
  none = '无',
  ai = 'AI智能',
  custom = '手动上传',
}

// 资料整理
export enum CreateSubmissionInformationType {
  ai = 'search',
  upload = 'upload',
}

export enum AuthorTypeChoices {
  longtext = 'longtext', //长文
  paper = 'paper', //论文
  shorttext = 'shorttext', //短文
  studynotes = 'studynotes', //学习笔记
}

export enum CreateContentType {
  ppt = 'ppt',
  paper = 'paper',
  image = 'image',
  question = 'question',
  conetnt = 'conetnt',
  article = 'article',
  information = 'information', //背景信息整理
}

export enum SubmissionStage {
  studying = 'studying',
  drafting = 'drafting',
  completing = 'completing',
  creating_chapters = 'creating_chapters',
  creating_outline = 'creating_outline',
}

export enum CreateContentMode {
  gpt35 = 'gpt35',
  gpt4 = 'gpt4',
}

export enum GoodsListType {
  normal = 1,
  gift = 2, //礼品卡列表
  knowledgeAssistant = 9, //知识库商品列表
  yearlyPackage = 11, //知识库连续包年 年度会员套餐
  teamVip = 13, //团队会员
}

// type: 1硬币，2礼品卡，3联系信息，5首冲使用，4暂时没值
export enum GoodsInfoType {
  coin = 1,
  gift = 2,
  contact = 3,
  firstRecharge = 5,
}

// 助力状态  null.不可助力 1.可助力 2.助力中 3.助力成功
export enum SubmissionHelpStatus {
  canHelp = 1,
  inHelp = 2,
  successHelp = 3,
}

export enum ImageClippingType {
  magicAvatar = 'magicAvatar',
  work = 'work',
}

export enum CreateSubmissionType {
  Submission_Ppt = 'Submission_Ppt',
  Submission_Paper = 'Submission_Paper',
  Submission_Avatar = 'Submission_Avatar',
  Submission_Question = 'Submission_Question',
}

export enum ConversationItemPageType {
  square = 'square',
}

export enum StepOneCreateStatus {
  create = 'create', // 创建
  waitPay = 'wait_pay', // 等待支付
}

export enum CreateOperationGroupType {
  creator = 'creator',
  category = 'category',
}

export enum MindmapAnswerType {
  outline = 'outline',
  mindmap = 'mindmap',
}

export enum RechargeOrderStatus {
  waitPay = 1, //待支付
  cancel = 2, //取消
  done = 3, //完成
  refund = 4, //退款
  portionRefund = 5, //部分退款，前端用不上
  replenishAddress = 6, //补充收货地址
  waitConsignment = 7, //待发货
  waitReceiving = 8, //已发货、待收货
  received = 9, //已收货
}
export enum QuestionLabelUnCheckedEnum {
  normal = '基础通用模型',
  noUseSearch = '未联网',
  noUseRepository = '未联知识库',
  noScholar = '未联学术搜索',
}
export enum QuestionLabelEnum {
  useR1 = '深度思考(R1)',
  useSearch = '联网搜索',
  useRepository = '知识库搜索',
  scholar = '学术搜索',
}

export enum QuestionTypeEnum {
  useR1 = 'useR1',
  useSearch = 'useSearch',
  useRepository = 'useRepository',
  useScholar = 'useScholar',
}

export enum SubmissionSelectedUploadFileType {
  none = 'none',
  local = 'local',
  knowledge = 'knowledge',
}

export enum InstallDesktopCheckStatus {
  unchecked = 0,
  installed = 1,
  notInstalled = 2,
}


// seo 主站
export const seo = 'https://www.xiaoin.com.cn/'


// 用户协议
export const userAgreement = isXiaoin
  ? 'https://oldstarlove.starringshop.com/packages/pages/privacy/privacy?id=30'
  : 'https://oldstarlove.starringshop.com/packages/pages/privacy/privacy?id=16'
//隐私政策
export const userPrivacy = isXiaoin
  ? 'https://oldstarlove.starringshop.com/packages/pages/privacy/privacy?id=29'
  : 'https://oldstarlove.starringshop.com/packages/pages/privacy/privacy?id=17'
// 写作攻略的链接更改： https://nyswtyuoai.feishu.cn/docx/CS2LdOpmWodZn9xGwjnc51ndnbe
export const creationGuide = isXiaoin
  ? 'https://riupaqr29r4.feishu.cn/wiki/Jwx6wgk19iLFouk0akrcgjThngh?from=from_copylink'
  : 'https://nyswtyuoai.feishu.cn/docx/CS2LdOpmWodZn9xGwjnc51ndnbe'
// 少于1000字的文档不进行学习
export const MIN_WORDS_COUNT = 100
export const RECHARGE_SERVICE_AGREEMENT_INSCHOOL =
  'https://oldstarlove.starringshop.com/packages/pages/privacy/privacy?id=22'
export const RECHARGE_SERVICE_AGREEMENT_XIAOIN =
  'https://oldstarlove.starringshop.com/packages/pages/privacy/privacy?id=31'
export const MEMBER_RECHARGE_SERVICE_AGREEMENT_XIAOIN =
  'https://oldstarlove.starringshop.com/packages/pages/privacy/privacy?id=32'
export const MEMBER_RECHARGE_SERVICE_AGREEMENT_INSCHOOL =
  'https://oldstarlove.starringshop.com/packages/pages/privacy/privacy?id=32'
export const PPT_ABSTRACT_PAGE_NUMBER = 5

export const rechargeServiceAgreement = isXiaoin
  ? RECHARGE_SERVICE_AGREEMENT_XIAOIN
  : RECHARGE_SERVICE_AGREEMENT_INSCHOOL

export const memberRechargeServiceAgreement = isXiaoin
  ? MEMBER_RECHARGE_SERVICE_AGREEMENT_XIAOIN
  : MEMBER_RECHARGE_SERVICE_AGREEMENT_INSCHOOL
export enum PayChannelType {
  ybxz = 11, //一笔写作
  lenovo = 14, //联想支付
  xiaoin = 13, //汇写科技
  xiaoinAssistant = 9, //小in助手
  lenovomm = 15, //联想小程序支付
  lenovoDeskTop = 16, //联想应用商店支付
  qihu360 = 17, //qihu360
  huawei = 18, //华为pc联运
  thunderobot = 19, //雷神 thunderobot&sharerUserId=1904725616016420866&phone=MXNwYmhkK3hYcnRycExvUmRUMXZHZz09OjpBexmG5041efQGPf7Rh34T
}
export enum KnowledgeAssistantType {
  introduction = 'introduction', //导读
  mind = 'mind', //思维导图
  translate = 'translate', //翻译
  note = 'note', //笔记
  askQuestion = 'askQuestion', //搜索
}
export enum KnowledgeAssistantTypeCn {
  introduction = '导读', //导读
  mind = '思维导图', //思维导图
  translate = '翻译', //翻译
  note = '笔记', //笔记
  askQuestion = '提问', //搜索
}


export enum NewKnowledgeAssistantType {
  guide = 'guide', //导读
  mindmap = 'mindmap', //思维导图
  translation = 'translation', //翻译
  notes = 'notes', //笔记
  questions = 'questions', //搜索
  download = '下载', //下载
  rename = '重命名', // 重命名
  move = '移动' // 移动
}


export enum NewKnowledgeAssistantTypeCn {
  guide = '导读', //导读
  mindmap = '思维导图', //思维导图
  translation = '翻译', //翻译
  notes = '笔记', //笔记
  questions = '提问', //提问
  download = '下载', //下载
  rename = '重命名', // 重命名
  move = '移动' // 移动
}



export const KnowledgeAssistantMemberVipCode = {
  A: 'A',
  B1: 'B1',
  B2: 'B2',
  B3: 'B3',
  C0: 'C0',
  C1: 'C1',
  C2: 'C2',
  C3: 'C3',
  C4: 'C4',
  C5: 'C5',
  C6: 'C6',
  C0EX: 'C0EX',
  C1EX: 'C1EX',
  C2EX: 'C2EX',
  C3EX: 'C3EX',
  C4EX: 'C4EX',
  E: 'E',
}

export const VipLevelNumber = {
  level0: 0,
  level1: 0.5,
  level2: 1,
  level3: 2,
  level4: 3,
}

export const KnowledgeAssistantMemberLevel = {
  level0: '//static-**********.file.myqcloud.com/xiaoin-h5/image/knowledge-assistant-level0.png',
  level2: '//static-**********.file.myqcloud.com/xiaoin-h5/image/knowledge-assistant-level2.png',
  level3: '//static-**********.file.myqcloud.com/xiaoin-h5/image/knowledge-assistant-level3.png',
  level4: '//static-**********.file.myqcloud.com/xiaoin-h5/image/knowledge-assistant-level4.png',
}
export enum PagePath {
  DailyNews = '/news', //每日资讯
  DailyNewsDetail = '/news', //每日资讯详情
  AiQuestioning = '/chat', //AI提问
  MyKnowledge = '/library', //我的知识库
  MyKnowledgeDetail = '/library', //知识库详情
}

export const KnowledgeFileIcon = {
  encode: 'https://static-**********.file.myqcloud.com/xiaoin-h5/image/library/encode-icon.png',
  text: 'https://static-**********.file.myqcloud.com/xiaoin-h5/image/library/text-icon.png',
  world: 'https://static-**********.file.myqcloud.com/xiaoin-h5/image/library/world-icon.png',
  ppt: 'https://static-**********.file.myqcloud.com/xiaoin-h5/image/library/ppt-icon.png',
  pdf: 'https://static-**********.cos.ap-shanghai.myqcloud.com/xiaoin-h5/image/pdf-icon.png',
  doc: 'https://static-**********.file.myqcloud.com/xiaoin-h5/image/library/world-icon.png',
  docx: 'https://static-**********.file.myqcloud.com/xiaoin-h5/image/library/world-icon.png',
  pptx: 'https://static-**********.file.myqcloud.com/xiaoin-h5/image/library/ppt-icon.png',
  md: 'https://static-**********.file.myqcloud.com/xiaoin-h5/image/library/text-icon.png',
  img: 'https://static-**********.file.myqcloud.com/xiaoin-h5/image/library/img-icon.png',
  xlsx: 'https://static-**********.file.myqcloud.com/xiaoin-h5/image/library/xlsx-icon.png',
}

export const KnowledgeFileIconSvg = {
  encode: 'code',
  text: 'txt',
  world: 'word',
  ppt: 'ppt',
  pdf: 'pdf',
  doc: 'word',
  docx: 'word',
  pptx: 'ppt',
  md: 'txt',
  img: 'img',
  xlsx: 'xls',
}

export enum KnowledgeFileSuffix {
  PDF = 'pdf',
  TXT = 'txt',
  MD = 'md',
  HTML = 'html',
  JPG = 'jpg',
  JPEG = 'jpeg',
  PNG = 'png',
  PPT = 'ppt',
  PPTX = 'pptx',
  XLSX = 'xlsx',
  XLS = 'xls',
  CSV = 'csv',
  DOC = 'doc',
  DOCX = 'docx',
}
export enum KnowledgeFileType {
  file = 'file', //导读
  html = 'html', //思维导图
}
export const getKnowledgeFileType = {
  file: '文档',
  html: '网页',
}

export const getLearningProgress = {
  init: '进行中',
  done: '完成',
}

export const vipLevelName = {
  0: '普通用户',
  0.5: '体验会员',
  1: '标准会员',
  2: '高级会员',
  3: '至尊会员',
}
export const cdnBaseUrl = '//static-**********.file.myqcloud.com'
export enum KnowledgeFileProcessCode {
  NO_ENOUGH_WORD = 'NO_ENOUGH_WORD', //字数不足的
  NO_DOCUMETS = 'NO_DOCUMETS', //无法读取内容
  FILE_TOO_FEW_WORD = 'FILE_TOO_FEW_WORD', //文档字数太少
  AUDITING_ERROR = 'AUDITING_ERROR', //文本审核失败
  NO_ENOUGH_SPACE = 'NO_ENOUGH_SPACE', //空间不足
}

export enum KnowledgeAssistantUpgradeType {
  KNOWLEDGE_NO_ENOUGH_WORD = 'KNOWLEDGE_NO_ENOUGH_WORD', //知识库字数
  QUESTION_NO_ENOUGH_WORD = 'QUESTION_NO_ENOUGH_WORD', //搜索字数
}

export enum HomeQuestionInputModal {
  AI_CREATE = 'AI_CREATE', //AI写作
  AI_QUESTION = 'AI_QUESTION', //AI提问
  ACADEMIC_SEARCH = 'ACADEMIC_SEARCH', //学术搜索
}

export enum PageTranslateStatusEnum {
  notStarted = 1, //文档未翻译
  hasStarted = 2, //文档已开始翻译
}

export enum FileTranslateStatusEnum {
  noTranslation = 0, //未开始
  inTranslation = 1, //翻译中
  translationEnd = 2, //翻译结束
  translationError = 3, //翻译异常失败
}
// 联想应用商店 支付appid:1369590227768960, 分享shareUserId:1821852989871124482
export const LENOVO_DESKTOP_SHARER_USER_ID = '1821852989871124482'

export const LARGEWORKER_DURATION = 10 * 1000
export const SMALLWORKER_DURATION = 3 * 1000

export const MAX_REFERENCE_TEMPLATE_FILE_COUNT = 3 //章节文件
export const MAX_REFERENCE_FILE_COUNT = 50 //ai写书参考资料库文件最大上传个数

export enum RepositoryFileStatus {
  DONE = 'done',
  INIT = 'init',
  ERROR = 'error',
}

export const TOKENNAME = 'Authorization'
export const AUTO_LOGIN_TOKEN = 'auto_login_token'
export const stroageKeyOfCurrentAutoLoginUser = 'currentAutoLoginInfo'
export const stroageKeyOfCurrentUser = 'currentLoginInfo'
export const stroageKeyOfWelcomeAlreadyShowed = 'welcomeAlreadyShowed'
export const stroageKeyOfLastDateNewAskedUserPopupShowed = 'lastDateNewAskedUserPopupShowed'
export const stroageKeyOfDraftPrefix = 'draftPrefix_'
export const stroageKeyOfFollowMpCardInChat = 'followMpCardInChat'

export const stroageKeyOfLastChatRobotCode = 'lastChatRobotCode'
export const stroageKeyOfLastChatMode = 'lastChatMode'

export const stroageKeyOfHomeNoticebarFeatures = 'homeNoticebarFeatures'

export const LS_KEY_OPEN_ID = 'openid'
export const LS_KEY_CONTACT_METHOD = 'contact_method'

export const stroageKeyOfImageClippingUrl = 'imageClippingUrl'

export const stroageKeyOfTodayCheckPhoneDate = 'todayCheckPhoneDate'
export const stroageKeyOfTodayCheckCount = 'todayCheckCount'

export const stroageKeyOfIndexCreateShouldShowButton = 'indexCreateShouldShowButton'

export const stroageKeyOfIsChromeAppInstalled = 'isChromeAppInstalled'

export const stroageKeyOfMiniappQrcode = 'stroageKeyOfMiniappQrcode' //邀请的小程序码

export enum BindPhoneModal {
  SHOW_BINDING = 'show',
  HIDE_BINDING = 'hide',
  FINISH_BINDING = 'finish',
}

export enum LastMessageStatus {
  error = 'error',
  thinking = 'thinking',
  answeing = 'answeing',
  end = 'end',
  done = 'done'
}


export enum RechargeModalTab {
  vip = 'vip',
  team = 'team',
  coin = 'coin'
}

export enum AsyncDataRequestStatusEnum {
  IDLE = 'IDLE',
  PENDING = 'PENDING',
  SUCCESS = 'SUCCESS',
  ERROR = 'ERROR',
}
export const seoConfigDefault = {
  title: '万能小in官网',
  description: '万能小in - 我的AI外脑',
  keywords: '万能小in, AI写作助手, 个人知识库, AI外脑'
}


// 侧边栏列表
export const sidebarTriggerList = [
  // 节点样式
  // {
  //   name: 'Node style',
  //   value: 'nodeStyle',
  //   icon: 'iconzhuti'
  // },
  // 基础样式
  // {
  //   name: 'Base style',
  //   value: 'baseStyle',
  //   icon: 'iconyangshi'
  // },
  // 主题
  {
    name: '模版',
    value: 'theme',
    icon: 'skin'
  },
  // 结构
  {
    name: '结构',
    value: 'structure',
    icon: 'cluster'
  },
  // 大纲
  {
    name: '大纲',
    value: 'outline',
    icon: 'unorderedList'
  },
  // 设置
  // {
  //   name: 'Setting',
  //   value: 'setting',
  //   icon: 'iconshezhi'
  // },
  // 快捷键
  {
    name: '快捷键',
    value: 'shortcutKey',
    icon: 'laptopOutlined'
  }
]

//侧边栏zIndex
export let sidebarZIndex = 1

const isMac = getPlatform().toUpperCase().indexOf('MAC') >= 0
const ctrl = isMac ? '⌘' : 'Ctrl'
const enter = isMac ? 'Return' : 'Enter'
const macFn = isMac ? 'fn + ' : ''
// 快捷键列表
export const shortcutKeyList = [
  {
    type: '节点操作',
    list: [
      {
        icon: 'icontianjiazijiedian',
        name: '插入下级节点',
        value: 'Tab | Insert'
      },
      {
        icon: 'iconjiedian',
        name: '插入同级节点',
        value: enter
      },
      {
        icon: 'icondodeparent',
        name: '插入父节点',
        value: 'Shift + Tab'
      },
      {
        icon: 'iconshangyi',
        name: '上移节点',
        value: `${ctrl} + ↑`
      },
      {
        icon: 'iconxiayi',
        name: '下移节点',
        value: `${ctrl} + ↓`
      },
      {
        icon: 'icongaikuozonglan',
        name: '插入概要',
        value: `${ctrl} + G`
      },
      {
        icon: 'iconzhankai',
        name: '展开/收起节点',
        value: '/'
      },
      {
        icon: 'iconshanchu',
        name: '删除节点',
        value: 'Delete | Backspace'
      },
      {
        icon: 'iconshanchu',
        name: '仅删除当前节点',
        value: 'Shift + Backspace'
      },
      {
        icon: 'iconfuzhi',
        name: '复制节点',
        value: `${ctrl} + C`
      },
      {
        icon: 'iconjianqie',
        name: '剪切节点',
        value: `${ctrl} + X`
      },
      {
        icon: 'iconniantie',
        name: '粘贴节点',
        value: `${ctrl} + V`
      },
      {
        icon: 'iconbianji',
        name: '编辑节点',
        value: macFn + 'F2'
      },
      {
        icon: 'iconhuanhang',
        name: '文本换行',
        value: `Shift + ${enter}`
      },
      {
        icon: 'iconhoutui-shi',
        name: '回退',
        value: `${ctrl} + Z`
      },
      {
        icon: 'iconqianjin1',
        name: '前进',
        value: `${ctrl} + Y`
      },
      {
        icon: 'iconquanxuan',
        name: '全选',
        value: `${ctrl} + A`
      },
      {
        icon: 'iconquanxuan',
        name: '多选',
        value: `右键 / ${ctrl} + 左键`
      },
      {
        icon: 'iconzhengli',
        name: '一键整理布局',
        value: `${ctrl} + L`
      },
      {
        icon: 'iconsousuo',
        name: '搜索和替换',
        value: `${ctrl} + F`
      }
    ]
  },
  {
    type: '画布操作',
    list: [
      {
        icon: 'iconfangda',
        name: '放大',
        value: `${ctrl} + +`
      },
      {
        icon: 'iconsuoxiao',
        name: '缩小',
        value: `${ctrl} + -`
      },
      {
        icon: 'iconfangda',
        name: '放大/缩小',
        value: `${ctrl} + 鼠标滚动`
      },
      {
        icon: 'icondingwei',
        name: '回到根节点',
        value: `${ctrl} + ${enter}`
      },
      {
        icon: 'iconquanping1',
        name: '适应画布',
        value: `${ctrl} + i`
      }
    ]
  },
  // {
  //   type: '大纲操作',
  //   list: [
  //     {
  //       icon: 'iconhuanhang',
  //       name: '文本换行',
  //       value: `Shift + ${enter}`
  //     },
  //     {
  //       icon: 'iconshanchu',
  //       name: '删除节点',
  //       value: 'Delete'
  //     },
  //     {
  //       icon: 'icontianjiazijiedian',
  //       name: '插入下级节点',
  //       value: 'Tab'
  //     },
  //     {
  //       icon: 'iconjiedian',
  //       name: '插入同级节点',
  //       value: enter
  //     },
  //     {
  //       icon: 'icondodeparent',
  //       name: '上移一个层级',
  //       value: 'Shift + Tab'
  //     }
  //   ]
  // }
]

export const mindMapLayoutImageList = {
  logicalStructureLeft: 'https://static-**********.cos.ap-shanghai.myqcloud.com/xiaoin-h5/image/mindmap/logicalStructureLeft.jpg',
  logicalStructure: 'https://static-**********.file.myqcloud.com/xiaoin-h5/image/mindmap/logicalStructure.png',
  mindMap: 'https://static-**********.file.myqcloud.com/xiaoin-h5/image/mindmap/mindMap.png',
  organizationStructure: 'https://static-**********.file.myqcloud.com/xiaoin-h5/image/mindmap/organizationStructure.png',
  catalogOrganization: 'https://static-**********.file.myqcloud.com/xiaoin-h5/image/mindmap/catalogOrganization.png',
  timeline: 'https://static-**********.file.myqcloud.com/xiaoin-h5/image/mindmap/timeline.png',
  timeline2: 'https://static-**********.file.myqcloud.com/xiaoin-h5/image/mindmap/timeline2.png',
  fishbone: 'https://static-**********.file.myqcloud.com/xiaoin-h5/image/mindmap/fishbone.png',
  verticalTimeline: 'https://static-**********.file.myqcloud.com/xiaoin-h5/image/mindmap/verticalTimeline.png'
}

const MINDMAP_LAYOUT = {
  LOGICAL_STRUCTURE: 'logicalStructure',
  LOGICAL_STRUCTURE_LEFT: 'logicalStructureLeft',
  MIND_MAP: 'mindMap',
  ORGANIZATION_STRUCTURE: 'organizationStructure',
  CATALOG_ORGANIZATION: 'catalogOrganization',
  TIMELINE: 'timeline',
  TIMELINE2: 'timeline2',
  FISHBONE: 'fishbone',
  VERTICAL_TIMELINE: 'verticalTimeline'
}

//  布局结构列表
export const mindMapLayoutList = [
  {
    name: '逻辑结构图',
    value: MINDMAP_LAYOUT.LOGICAL_STRUCTURE
  },
  {
    name: '向左逻辑结构图',
    value: MINDMAP_LAYOUT.LOGICAL_STRUCTURE_LEFT
  },
  {
    name: '思维导图',
    value: MINDMAP_LAYOUT.MIND_MAP
  },
  {
    name: '组织结构图',
    value: MINDMAP_LAYOUT.ORGANIZATION_STRUCTURE
  },
  {
    name: '目录组织图',
    value: MINDMAP_LAYOUT.CATALOG_ORGANIZATION
  },
  {
    name: '时间轴',
    value: MINDMAP_LAYOUT.TIMELINE
  },
  {
    name: '时间轴2',
    value: MINDMAP_LAYOUT.TIMELINE2
  },
  {
    name: '竖向时间轴',
    value: MINDMAP_LAYOUT.VERTICAL_TIMELINE
  },
  {
    name: '鱼骨图',
    value: MINDMAP_LAYOUT.FISHBONE
  }
]


//团队成员角色
export enum TEAM_MEMBER_ROLE {
  OWNER = 'owner',
  ADMIN = 'admin',
  MEMBER = 'member',
}
export const getTeamMemberRoleName = {
  'owner': '创建者',
  'admin': '管理员',
  'member': '成员'
}
export const getTeamMemberStatusName = {
  'invited': '待加入',
  'active': '已加入',
  'inactive': '禁用',
  'declined': '拒绝',
}
export enum CREATE_CODE {
  BOOK = 'book',
  PPT = 'ppt'
}
export enum BOOK_PAY_TRIGGER_TYPE {
  CREATE_CHAPTER = 'CHAPTER', //创建本章节
  AI_EDITOR = 'AI_EDITOR', //AI编辑
  GENERATE_IMG = 'GENERATE_IMG',//图片生成
  GENERATE_TABLE = 'GENERATE_TABLE',//表格生成
  GENERATE_CHAT = 'GENERATE_CHART',//图表生成
  GENERATE_MATH = 'GENERATE_FORMULA',//公式生成
  GENERATE_SVG = 'GENERATE_SVG',//示意图生成
  GENERAT_TRAVERSE_CHAPTERS = 'ONE_CLICK_AUTHORING',//一键写作
  GENERATE_CHAPTER_ATTACHMENT = 'GENERATE_CHAPTER_ATTACHMENT',//上传参考资料
  ACADEMIC_SEARCH = 'ACADEMIC_SEARCH',//学术搜索
  ACADEMIC_FORMAT = 'ACADEMIC_FORMAT',//文献格式
  CHART_SORT = 'CHART_SORT',//一键图表排序
}


export const ACTION_CODE = {
  POLISH: 'polish',
  EXPAND: 'expand',
  SHORTEN: 'shorten',
  TRANSLATE: 'translate',
  SIMPLE: 'simple',
}
export const ACTION_CODE_NAME = {
  POLISH: '改写',
  EXPAND: '扩写',
  SHORTEN: '缩写',
  TRANSLATE: '翻译',
}

export const UTM_SOURCE_VALUE = {
  LENOVO: 'lenovo',
  QIHU360: 'qihu360',
  HUAWEI: 'huawei',
  THUNDEROBOT: 'thunderobot',
}
export const mobileSessionKey = 'utm_mobile'

export const channelType = {
  UNKNOWN: 0, // 未知
  SEM: 1, // 竞价广告
  SEO: 2, // 搜索引擎优化
  ORGANIC: 3, // 自然流量
  USERREFERRAL: 4, // 用户裂变
  SOCIALMEDIA: 5, // 自媒体
  KOL: 13, // 关键意见领袖
  BRANDAD: 14, // 品牌投放
  OFFICIAL: 15, // 官方渠道
  ASSOCIATION: 16, // 联想
  THUNDERGOD: 17, // 雷神
  SHEXIANG: 18, // 社想
  ZHUOYI: 19, // 卓易
  SHENBIAO: 20, // 深表
  HUAWEI: 21, // 华为
  BAIDUAIPLUS: 22, // 百度AI+计划
  BILIBILI: 23, // 哔哩哔哩
};

export enum LastAgentMessageStatus {
  error = 'error',
  conversation_completed = 'conversation.completed',
  conversation_started = 'conversation.started',
  conversation_error = 'conversation.error',
  text_done = 'text.done',
  text_delta = 'text.delta',
  think_delta = 'think.delta',
  think_done = 'think.done',
  text_start = 'text.start',
  tool_call_started = 'tool_call.started',
  tool_call_completed = 'tool_call.completed',
  answeing = 'answeing',
  end = 'end',
  done = 'done',
  message = 'message'
}

export const EditorContent = {
  Table: "editorTable",
  Image: "editorImage",
  Chart: "editorChart",
  Schematic: "editorSchematic"
}


export enum EditorContentType {
  editorTable = '表格',
  editorImage = '图片',
  editorChart = '图表',
  editorSchematic = '示意图'
}

export enum ChartType {
  bar = '柱状图',
  line = '折线图',
  pie = '饼图'
}

//会话来源
export enum SessionFrom {
  ///正常会话
  Normal = 'normal',
  ///知识库文件夹会话
  KnowledgeFolder = 'knowledge_folder',
  ///知识库单个文件会话
  KnowledgeSingleFile = 'knowledge_sigle_file',
}

export enum AgentToolName {
  ///读取文件
  repo_file_block = 'repo_file_block',
  ///搜索文本
  repo_search_block = 'repo_search_block',
  ///查询文件信息
  repo_search_fileinfo = 'repo_search_fileinfo',
  ///查询知识库文件
  repo_search_filename = 'repo_search_filename',
  ///阅读网页
  read_web_url = 'read_web_url',
  ///文献搜索工具
  scholar_search = 'scholar_search',

  ///阅读pdf
  read_pdf_url = 'read_pdf_url',
  ///网页搜索
  web_search = 'web_search',
}

export const AgentToolCName = {
  ///读取文件
  repo_file_block: '读取文件',
  ///搜索文本
  repo_search_block: '搜索文本',
  ///查询文件信息
  repo_search_fileinfo: '查询文件信息',
  ///查询知识库文件
  repo_search_filename: '查询知识库文件',
  ///阅读网页
  read_web_url: '阅读网页',
  ///文献搜索工具
  scholar_search: '文献搜索工具',
  ///阅读pdf
  read_pdf_url: '阅读pdf',
  ///网页搜索
  web_search: '网页搜索',
}

export const AgentToolReferenceName = {
  ///读取文件
  repo_file_block: '读取文件',
  ///搜索文本
  repo_search_block: '搜索文本',
  ///查询文件信息
  repo_search_fileinfo: '查询文件信息',
  ///查询知识库文件
  repo_search_filename: '知识库文件',
  ///阅读网页
  read_web_url: '阅读网页',
  ///文献搜索工具
  scholar_search: '文献搜索工具',
  ///阅读pdf
  read_pdf_url: '阅读pdf',
  ///网页搜索
  web_search: '联网资料',
}