// stores/user.ts
import { useChannelStore } from '@/stores/channelId'
import { BindPhoneModal, UTM_SOURCE_VALUE } from '@/utils/constants'
import { getUserInfo, loginMobile, loginThunderobot } from '~/api/user'
import type { AppUserInfo, KnowledgeAssistantMemberInfo } from '~/services/types/loginMobileRes'
import type { LoginThunderobotParams } from '~/services/types/user'
import { autoLoginThunderobot } from '~/utils/autoLogin'

export const useUserStore = defineStore('user', {
  state: () => {
    // 在 SSR 阶段尝试从 cookies 预填充状态
    let initialLoginInfo: AppUserInfo | null = null
    let initialKnowledgeAssistantInfo: KnowledgeAssistantMemberInfo | null = null

    // if (import.meta.server) {
    //   try {
    //     const { CookieAuthManager } = require('~/utils/cookie-auth')
    //     const authData = CookieAuthManager.getAuthData()
    //     initialLoginInfo = authData.userInfo || null
    //     initialKnowledgeAssistantInfo = authData.knowledgeAssistantInfo || null
    //   } catch (error) {
    //     // SSR 阶段 cookie 读取失败时使用默认值
    //     console.warn('SSR 阶段读取认证状态失败:', error)
    //   }
    // }

    return {
      currentLoginInfo: initialLoginInfo,
      knowledgeAssistantMemberInfo: initialKnowledgeAssistantInfo,
      isAcceptInstallationApp: false,
      installPromptEvent: null as Event | null,
      showPhoneBoundModal: {
        status: BindPhoneModal.HIDE_BINDING,
        confirmText: '绑定',
      },
      showLoginModal: false,
      clickId: '',
      channel: '',
      sourceId: '',
      qhclickId: '',
      openRecordUrl: '',
      track_id: '',
    }
  },
  getters: {
    isLogined: (state) => (state?.currentLoginInfo?.id ? true : false),
    isBindPhone: (state) => {
      if (!state?.currentLoginInfo) {
        return false
      }
      if (!state?.currentLoginInfo?.account || (state?.currentLoginInfo?.account || '') === '') {
        return false
      }
      return true
    },
    canAskQuestionCount: (state) => {
      if (!state?.knowledgeAssistantMemberInfo) {
        return 0
      }
      return (
        state.knowledgeAssistantMemberInfo.maxChat - state.knowledgeAssistantMemberInfo.usedChat
      )
    },
  },
  actions: {
    setLoginInfo(info: any) {
      this.currentLoginInfo = info
    },
    setKnowledgeAssistantMemberInfo(info: any) {
      this.knowledgeAssistantMemberInfo = info
    },

    setInstallPromptEvent(event: any) {
      this.installPromptEvent = event
    },
    clearInstallPromptEvent() {
      this.installPromptEvent = null
    },
    setShowPhoneBoundModal(params: any = {}) {
      this.showPhoneBoundModal = {
        ...this.showPhoneBoundModal,
        ...params,
      }
    },
    openLoginModal() {
      try {
        const utm_source = sessionStorage.getItem('utm_source')
        if (utm_source == UTM_SOURCE_VALUE.THUNDEROBOT) {
          autoLoginThunderobot()
          return
        }
      } catch (error) {
        console.error(error)
      }
      this.showLoginModal = true
    },
    closeLoginModal() {
      this.showLoginModal = false
    },
    async login(params: any) {
      if (StarloveUtil.isInTestServer()) {
        params.verifyCode = params.verifyCode == '777778' ? '7777779' : params.verifyCode
      }
      params.channelId = useChannelStore().getChannelId
      const result = await loginMobile(params)
      if (!result.success) {
        throw new Error(result.message || '登录失败')
      }

      // console.log('login result', result)
      this.setLoginInfo(result.data)
      storage.set(TOKENNAME, result.data?.token)

      // 同时将认证信息存储到 cookies 中以支持 SSR
      if (import.meta.client && result.data?.token) {
        const { CookieAuthManager } = await import('~/utils/cookie-auth')
        CookieAuthManager.setToken(result.data.token)
        CookieAuthManager.setLoginStatus(true)
      }

      await this.loadUserInfo()
      // 再安排5秒钟后获取一次
      setTimeout(() => {
        this.loadUserInfo()
      }, 5000)
    },
    async loadUserInfo() {
      try {
        const result = await getUserInfo()
        if (!result || !result.ok) {
          if (result.message == '用户已注销') {
            this.setLoginInfo(null)
            storage.remove(TOKENNAME)
            storage.remove('userInfo')
            storage.remove('userId')

            // 清理 cookies
            if (import.meta.client) {
              const { CookieAuthManager } = await import('~/utils/cookie-auth')
              CookieAuthManager.clearAll()
            }

            window.location.reload()
          }
          return
        }

        this.setLoginInfo(result.data)

        // 同步用户信息到 cookies
        if (import.meta.client && result.data) {
          const { CookieAuthManager } = await import('~/utils/cookie-auth')
          CookieAuthManager.setUserInfo(result.data)
          CookieAuthManager.setLoginStatus(true)
        }
      } catch (error) {
        console.error(error)
      }
    },
    setChannel(channel: string) {
      this.channel = channel
    },
    setOpenRecordUrl(url: string) {
      this.openRecordUrl = url
    },
    async loginThunde(params: LoginThunderobotParams) {
      const result = await loginThunderobot(params)
      if (!result.success) {
        throw new Error(result.message || '登录失败')
      }

      // console.log('login result', result)
      this.setLoginInfo(result.data)
      storage.set(TOKENNAME, result.data?.token)

      // 同时将认证信息存储到 cookies 中以支持 SSR
      if (import.meta.client && result.data?.token) {
        const { CookieAuthManager } = await import('~/utils/cookie-auth')
        CookieAuthManager.setToken(result.data.token)
        CookieAuthManager.setLoginStatus(true)
      }

      await this.loadUserInfo()
      // 再安排5秒钟后获取一次
      setTimeout(() => {
        this.loadUserInfo()
      }, 5000)
    },

    /**
     * 从 cookies 初始化用户状态（用于 SSR）
     */
    async initFromCookies() {
      if (import.meta.client) {
        const { CookieAuthManager } = await import('~/utils/cookie-auth')
        const authData = CookieAuthManager.getAuthData()

        if (authData.userInfo) {
          this.setLoginInfo(authData.userInfo)
        }
        if (authData.knowledgeAssistantInfo) {
          this.setKnowledgeAssistantMemberInfo(authData.knowledgeAssistantInfo)
        }
      }
    },
  },
  // persist: {
  //   storage: persistedState.localStorage, // 使用 localStorage 进行持久化
  // },
})
