import { message } from 'ant-design-vue'
import { defineStore } from 'pinia'
import { buyAcademicSearch } from '~/api/exchange'
import { UserService } from '~/services/user'

// 兑换选项类型
export interface ExchangeOption {
  times: number
  coins: number
  label: string
}

// 预设兑换选项
export const EXCHANGE_OPTIONS: ExchangeOption[] = [
  { times: 10, coins: 20000, label: '10次' },
  { times: 50, coins: 100000, label: '50次' },
  { times: 100, coins: 200000, label: '100次' }
]

// 兑换比例：每次搜索需要2000硬币
export const COINS_PER_SEARCH = 2000

export const useExchangeSearchStore = defineStore('exchangeSearch', {
  state: () => ({
    // 模态弹窗显示状态
    modalVisible: false,
    // 选中的兑换选项索引（0-2为预设选项，3为自定义）
    selectedOptionIndex: 0,
    // 自定义次数
    customTimes: 500,
    // 是否正在提交兑换
    isExchanging: false
  }),

  getters: {
    // 当前选择的次数
    selectedTimes: (state) => {
      if (state.selectedOptionIndex === 3) {
        return state.customTimes
      }
      return EXCHANGE_OPTIONS[state.selectedOptionIndex]?.times || 0
    },

    // 当前需要的硬币数量
    requiredCoins: (state) => {
      const times = state.selectedOptionIndex === 3
        ? state.customTimes
        : EXCHANGE_OPTIONS[state.selectedOptionIndex]?.times || 0
      return times * COINS_PER_SEARCH
    },

    // 用户当前硬币余额
    userCoinBalance: () => {
      const userInfo = UserService.getCurrentLoginInfo()
      return userInfo?.coinBalance || 0
    },

    // 是否硬币不足
    isInsufficientCoins(): boolean {
      return this.userCoinBalance < this.requiredCoins
    },

    // 兑换后剩余硬币
    remainingCoins(): number {
      return Math.max(0, this.userCoinBalance - this.requiredCoins)
    }
  },

  actions: {
    // 打开兑换模态弹窗
    openExchangeModal() {
      this.modalVisible = true
      this.selectedOptionIndex = 0
      this.customTimes = 500
    },

    // 关闭兑换模态弹窗
    closeExchangeModal() {
      this.modalVisible = false
      this.isExchanging = false
    },

    // 选择兑换选项
    selectOption(index: number) {
      this.selectedOptionIndex = index
    },

    // 设置自定义次数
    setCustomTimes(times: number) {
      // 限制范围在1-999之间
      this.customTimes = Math.max(1, Math.min(99999999, times))
    },

    // 执行兑换
    async performExchange() {
      if (this.isExchanging) return

      // 检查硬币是否足够
      if (this.isInsufficientCoins) {
        message.error('硬币余额不足，请先充值')
        return
      }

      // 检查次数是否有效
      if (this.selectedTimes <= 0) {
        message.error('请选择有效的兑换次数')
        return
      }

      this.isExchanging = true

      try {
        // 调用兑换API
        const result = await buyAcademicSearch({
          count: this.selectedTimes,
        })

        if (result.ok) {
          message.success(`成功兑换${this.selectedTimes}次学术搜索次数`)

          // 更新用户信息
          await UserService.loadUserInfoAndAssistantMemberInfo()

          // 关闭模态弹窗
          this.closeExchangeModal()
        } else {
          message.error(result.message || '兑换失败，请稍后重试')
        }

      } catch (error) {
        console.error('兑换失败:', error)
        message.error('兑换失败，请稍后重试')
      } finally {
        this.isExchanging = false
      }
    }
  }
})
