import { message } from 'ant-design-vue'
import { defineStore } from 'pinia'
import { getTeamList } from '~/api/user'
import type { TeamInfo } from '~/services/types/user'

export const useSwitchAccountStore = defineStore('switchAccount', {
    state: () => ({
        isModalOpen: false,
        isLoading: false,
        isHideSelectText: true,
        accounts: [] as TeamInfo[],
        lastActionTime: 0,
    }),

    getters: {
        currentAccount: (state) => state.accounts.find(account => account.isCurrent),
        otherAccounts: (state) => state.accounts.filter(account => !account.isCurrent),
    },

    actions: {

        async fetchTeamData() {
            try {
                const res = await getTeamList()
                if (!res.ok) {
                    message.error(res.message || '团队账号获取失败')
                    return []
                }

                if (!res.data || !Array.isArray(res.data)) {
                    message.error('团队账号获取错误')
                    return []
                }
                return res.data || []
            } catch (error) {
                message.error('团队账号获取异常')
                return []
            }
        },

        // fetchForce 是否强制刷新
        async fetchAccounts(fetchForce = false) {
            if (this.accounts.length > 0 && !fetchForce) return this.accounts

            this.isLoading = true
            let result: any = []
            try {
                const teamList = await this.fetchTeamData()
                if (teamList.length == 0) {
                    return
                }
                this.accounts = teamList[0]

                result = teamList
            } catch (error) {
                message.error('获取账号列表失败')
                console.error('获取账号列表失败:', error)

            } finally {
                this.isLoading = false
                return result
            }
        },
    }
}) 