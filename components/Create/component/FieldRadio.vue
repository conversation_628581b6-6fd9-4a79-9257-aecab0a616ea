<template>
    <div>
        <div class="mb-8" v-if="shouldShowSizeField">
            <label class="flex items-center text-sm font-medium text-gray-700 mb-2">
                <span class="text-red-500 mr-1" v-if="fieldItem.isRequired == 'Y'">*</span>
                {{ fieldItem.fieldName }}
                <PopoverHelp :content="fieldItem.description" />
            </label>
            <div class="flex gap-3 mb-4">
                <div v-for="item in getFieldItemList()" :key="item.value" class="flex-1 max-w-[218px] min-w-[120px]">
                    <div @click="!isItemDisabled(item) && handleChangeRadio(item.value)" :class="[
                        'border rounded-lg p-2 cursor-pointer transition-colors text-center',
                        radioValue === item.value ? 'border-blue-500 bg-blue-50/50' : 'border-gray-200',
                        isItemDisabled(item) ? 'cursor-not-allowed opacity-50' : ''
                    ]">
                        <div class="flex items-center justify-start gap-2">
                            <div :class="[
                                'w-8 h-6 rounded-md flex items-center justify-center',
                                getTagColorClass(item.value)
                            ]">
                                <span class="text-sm font-medium" :class="getTagTextColorClass(item.value)">
                                    {{ getFieldItemTag(item) }}
                                </span>
                            </div>
                            <div>
                                <div class="text-xs text-gray-500">{{ item.name }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</template>

<script lang="ts" setup>
import PopoverHelp from '~/components/Common/PopoverHelp.vue';
import { CreateSubmissionAssistType } from '~/utils/constants';

const props = defineProps({
    code: {
        type: String,
        required: true
    },
    fieldItem: {
        type: Object,
        required: true
    },
    radioValue: {
        type: String,
        required: true
    },
    outlineType: {
        type: String,
        required: true
    },
    isUploadOrSelectedKnowledgeFile: {
        type: Boolean,
        required: true
    }
})

console.log('FieldRadio props:', props)

console.log('radioValue:', props.radioValue)

watch(() => props.radioValue, (newValue) => {
    console.log('radioValue changed:', newValue)
}, { immediate: true })

const emit = defineEmits(['update:radioValue', 'typeChange'])

const isItemDisabled = (item: any) => {
    return props.isUploadOrSelectedKnowledgeFile && item.name.startsWith('超短');
};

const isCustomAndSize = computed(() => {
    if (props.outlineType == CreateSubmissionAssistType.custom && props.fieldItem.fieldCode == 'size') {
        return true
    }
    return false
})

const isPPTAndSize = computed(() => {
    if (props.code == 'ppt' && props.fieldItem.fieldCode == 'size') {
        return true
    }
    return false
})

const shouldShowSizeField = computed(() => {
    if (isCustomAndSize.value) {
        return false
    }
    if (isPPTAndSize.value) {
        return false
    }
    return true
})



const handleChangeRadio = (value: any) => {
    emit('update:radioValue', value)

    // 如果是 title 字段，发送类型变化事件
    if (props.fieldItem.fieldCode === 'title') {
        emit('typeChange', value)
    }
}

const getFieldItemList = () => {
    const item = props.fieldItem
    if (!item.options) {
        return []
    }
    const list = item.options.split(',')
    const data = list.map((child: any) => {
        return { name: child, value: child }
    })
    return data
}

const getFieldItemFirstWord = (child: any) => {
    if (child.indexOf('超短') > -1) {
        return child.substring(0, 2)
    }
    return child.substring(0, 1)
}

// 获取标签文本
const getFieldItemTag = (item: any) => {
    if (item.value === "中文") {
        return '中'
    }
    if (item.value === "英文") {
        return 'En'
    }

    if (props.fieldItem.code == 'insight_chart') {
        item.name = EditorContentType[item.name as keyof typeof EditorContentType] || ''
    }

    return getFieldItemFirstWord(item.name)
}

// 获取标签背景颜色
const getTagColorClass = (value: string) => {
    if (value.startsWith('中文')) {
        return 'bg-red-50'
    }
    if (value.startsWith('英文')) {
        return 'bg-blue-50'
    }
    if (value.startsWith('超短')) {
        return 'bg-purple-50'
    }
    if (value.startsWith('短')) {
        return 'bg-green-50'
    }
    if (value.startsWith('中')) {
        return 'bg-yellow-50'
    }
    if (value.startsWith('长')) {
        return 'bg-orange-50'
    }
    return 'bg-gray-50'
}

// 获取标签文字颜色
const getTagTextColorClass = (value: string) => {
    if (value.startsWith('中文')) {
        return 'text-red-600'
    }
    if (value.startsWith('英文')) {
        return 'text-blue-600'
    }
    if (value.startsWith('超短')) {
        return 'text-purple-600'
    }
    if (value.startsWith('短')) {
        return 'text-green-600'
    }
    if (value.startsWith('中')) {
        return 'text-yellow-600'
    }
    if (value.startsWith('长')) {
        return 'text-orange-600'
    }
    return 'text-gray-600'
}

watchEffect(() => {
    if (props.isUploadOrSelectedKnowledgeFile && props.radioValue.startsWith('超短')) {
        emit('update:radioValue', '')
    }
})

onMounted(() => {
})

</script>
