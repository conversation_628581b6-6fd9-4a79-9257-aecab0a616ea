<template>
    <!-- 左侧筛选区域 -->
    <div class="w-1/5 flex flex-col space-y-6 p-4 flex-shrink-0">
        <!-- 时间筛选 -->
        <div class="flex flex-col space-y-3 pl-[10%] text-sm">
            <div class="flex flex-col space-y-2">
                <div class="cursor-pointer" @click="selectTimeRange('')"
                    :class="filters.timeRange === '' ? 'text-[#2551B5] font-medium' : 'text-[#666]'">
                    时间不限
                </div>
                <div v-for="item in timeTabs" :key="item.time" class="cursor-pointer"
                    @click="selectTimeRange(item.time)"
                    :class="filters.timeRange === item.time ? 'text-[#2551B5] font-medium' : 'text-[#666]'">
                    {{ item.name }}
                </div>
                <div class="text-[#666] cursor-pointer hover:text-[#333]" @click="toggleCustomRange">
                    自定义范围...
                </div>
                <div v-if="showCustomRange" class="mt-2" ref="customRangeRef">
                    <!-- 自定义年份选择器 -->
                    <div class="bg-white rounded-lg p-3 shadow-sm" @click.stop>
                        <!-- 年份选择区域 -->
                        <div class="flex mb-3">
                            <!-- 开始年份 -->
                            <div class="flex-1">
                                <div class="h-28 overflow-y-auto custom-scrollbar">
                                    <div v-for="year in yearOptions" :key="year"
                                        class="px-2 py-1.5 cursor-pointer hover:bg-gray-50 text-center text-sm"
                                        :class="tempStartYear === year ? 'bg-blue-50 text-blue-600 font-medium' : ''"
                                        @click="tempStartYear = year">
                                        {{ year }}
                                    </div>
                                </div>
                            </div>

                            <!-- 结束年份 -->
                            <div class="flex-1">
                                <div class="h-28 overflow-y-auto custom-scrollbar">
                                    <div v-for="year in yearOptions" :key="year"
                                        class="px-2 py-1.5 cursor-pointer hover:bg-gray-50 text-center text-sm"
                                        :class="tempEndYear === year ? 'bg-blue-50 text-blue-600 font-medium' : ''"
                                        @click="tempEndYear = year">
                                        {{ year }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 按钮区域 -->
                        <div class="flex justify-end space-x-2">
                            <button
                                class="px-3 py-1 text-sm text-gray-600 border border-gray-300 rounded hover:bg-gray-50"
                                @click="clearCustomRange">
                                清除
                            </button>
                            <button class="px-3 py-1 text-sm text-white bg-blue-600 rounded hover:bg-blue-700"
                                @click="confirmCustomRange">
                                确定
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 类型筛选 -->
        <div class="flex flex-col space-y-3 pl-[10%] text-sm">
            <div class="font-medium text-[#333]">文章类型</div>
            <div class="flex flex-col space-y-2">
                <div class="cursor-pointer" @click="selectArticleType(0)"
                    :class="filters.as_rr === 0 ? 'text-[#2551B5] font-medium' : 'text-[#666]'">
                    类型不限
                </div>
                <div class="cursor-pointer" @click="selectArticleType(1)"
                    :class="filters.as_rr === 1 ? 'text-[#2551B5] font-medium' : 'text-[#666]'">
                    评论性文章
                </div>
            </div>
        </div>

        <!-- 排序方式 -->
        <div class="flex flex-col space-y-3 pl-[10%] text-sm">
            <div class="flex flex-col space-y-2">
                <div class="cursor-pointer" @click="selectSortType(0)"
                    :class="filters.scisbd === 0 ? 'text-[#2551B5] font-medium' : 'text-[#666]'">
                    按相关性排序
                </div>
                <div class="cursor-pointer" @click="selectSortType(1)"
                    :class="filters.scisbd === 1 ? 'text-[#2551B5] font-medium' : 'text-[#666]'">
                    按日期排序
                </div>
            </div>
        </div>


        <!-- 专利和引用筛选 -->
        <!-- <div class="flex flex-col space-y-3 pl-[10%] text-sm">
            <div class="font-medium text-[#333]">其他选项</div>
            <div class="flex flex-col space-y-2">
                是否包含专利
                <div class="flex items-center space-x-2 cursor-pointer" @click="togglePatents">
                    <div class="w-4 h-4 border border-gray-400 rounded-sm flex items-center justify-center"
                        :class="filters.includePatents ? 'bg-[#2551B5] border-[#2551B5]' : 'bg-white'">
                        <svg v-if="filters.includePatents" class="w-3 h-3 text-white" fill="currentColor"
                            viewBox="0 0 20 20">
                            <path fill-rule="evenodd"
                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <span :class="filters.includePatents ? 'text-[#2551B5] font-medium' : 'text-[#666]'">
                        包含专利
                    </span>
                </div>

                是否包含引用
                <div class="flex items-center space-x-2 cursor-pointer" @click="toggleCitations">
                    <div class="w-4 h-4 border border-gray-400 rounded-sm flex items-center justify-center"
                        :class="filters.includeCitations ? 'bg-[#2551B5] border-[#2551B5]' : 'bg-white'">
                        <svg v-if="filters.includeCitations" class="w-3 h-3 text-white" fill="currentColor"
                            viewBox="0 0 20 20">
                            <path fill-rule="evenodd"
                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <span :class="filters.includeCitations ? 'text-[#2551B5] font-medium' : 'text-[#666]'">
                        包含引用
                    </span>
                </div>
            </div>
        </div> -->
    </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref } from 'vue';

// Props 定义
interface Props {
    filters: any;
}

const props = defineProps<Props>();

// Emits 定义
const emit = defineEmits<{
    filterChange: [];
}>();

// 自定义时间范围
const showCustomRange = ref(false);
const customRangeRef = ref<HTMLElement | null>(null);

// 年份选择器相关
const tempStartYear = ref<number | null>(null);
const tempEndYear = ref<number | null>(null);

// 生成年份选项（从1990年到当前年份+5年）
const yearOptions = ref<number[]>([]);
const initYearOptions = () => {
    const currentYear = new Date().getFullYear();
    for (let year = 1990; year <= currentYear + 5; year++) {
        yearOptions.value.push(year);
    }
};
initYearOptions();

// 获取当前年份
const currentYear = new Date().getFullYear();

// 构建时间选项数组
const timeTabs = ref([
    {
        name: `${currentYear}以来`,
        time: currentYear.toString()
    },
    {
        name: `${currentYear - 1}以来`,
        time: (currentYear - 1).toString()
    },
    {
        name: `${currentYear - 3}以来`,
        time: (currentYear - 3).toString()
    }
]);

// 处理筛选条件变化
const handleFilterChange = () => {
    emit('filterChange');
};

// 选择时间范围
const selectTimeRange = (timeValue: string) => {
    props.filters.timeRange = timeValue;
    // 如果选择时间不限，重置时间参数
    if (timeValue === '') {
        props.filters.as_ylo = 0;
        props.filters.as_yhi = 0;
    }
    // 关闭自定义范围选择器
    showCustomRange.value = false;
    handleFilterChange();
};

// 切换自定义范围显示
const toggleCustomRange = () => {
    showCustomRange.value = !showCustomRange.value;
};

// 选择文章类型
const selectArticleType = (typeValue: number) => {
    props.filters.as_rr = typeValue;
    handleFilterChange();
};

const selectSortType = (typeValue: number) => {
    props.filters.scisbd = typeValue;
    handleFilterChange();
};

// 切换专利选择
const togglePatents = () => {
    props.filters.includePatents = !props.filters.includePatents;
    props.filters.as_sdt = props.filters.includePatents ? 7 : 0;
    handleFilterChange();
};

// 切换引用选择
const toggleCitations = () => {
    props.filters.includeCitations = !props.filters.includeCitations;
    props.filters.as_vis = props.filters.includeCitations ? 1 : 0;
    handleFilterChange();
};

// 清除自定义时间范围
const clearCustomRange = () => {
    tempStartYear.value = null;
    tempEndYear.value = null;
    props.filters.as_ylo = 0;
    props.filters.as_yhi = 0;
    props.filters.timeRange = '';
    showCustomRange.value = false;
    handleFilterChange();
};

// 确定自定义时间范围
const confirmCustomRange = () => {
    if (tempStartYear.value && tempEndYear.value) {
        // 确保开始年份不大于结束年份
        if (tempStartYear.value > tempEndYear.value) {
            const temp = tempStartYear.value;
            tempStartYear.value = tempEndYear.value;
            tempEndYear.value = temp;
        }

        props.filters.as_ylo = tempStartYear.value;
        props.filters.as_yhi = tempEndYear.value;
        props.filters.timeRange = 'custom';
        showCustomRange.value = false;
        handleFilterChange();
    }
};

// 点击外部关闭自定义范围选择器
const handleClickOutside = (event: Event) => {
    if (customRangeRef.value && !customRangeRef.value.contains(event.target as Node)) {
        showCustomRange.value = false;
    }
};

onMounted(() => {
    // 添加点击外部事件监听器
    document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
    // 移除事件监听器
    document.removeEventListener('click', handleClickOutside);
});
</script>

<style scoped>
.custom-scrollbar::-webkit-scrollbar {
    width: 4px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 2px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}

/* Firefox */
.custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #d1d5db transparent;
}
</style>
