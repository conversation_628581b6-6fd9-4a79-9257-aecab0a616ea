<template>
    <div style="border-radius: 15px 15px 0px 0px;" class="flex-1 overflow-hidden bg-[#FFFFFF] flex flex-col">
        <!-- 上 -->
        <div class="w-full h-[60px] p-4 flex items-center border-b border-[#F3F3F3] flex-shrink-0">
            <!-- 左 -->
            <div class="w-1/5 flex items-center space-x-2">
                <Iconfont name="xueshuwenzhang" :size="15"></Iconfont>
                <span class="text-[#333333] text-sm">文章</span>
            </div>

            <!-- 右 -->
            <div class="w-4/5 text-[#999999] text-sm">
                找到约 {{ page.total }} 条结果
            </div>
        </div>

        <!-- 下 -->
        <div class="flex-1 flex overflow-hidden">
            <!-- 左侧筛选区域 -->
            <ScholarFilterSidebar :filters="filters" @filter-change="handleFilterChange" />

            <!-- 右侧内容区域 -->
            <ScholarResultsContent :loading="loading" :cholar-list="cholarList" :search-query="searchQuery" :page="page"
                @go-to-page="goToPage" />
        </div>
    </div>
</template>

<script setup lang="ts">
import { scholarSearch } from '@/api/scholar';
import Iconfont from '@/components/Iconfont.vue';
import { message } from 'ant-design-vue';
import { reactive, ref } from 'vue';
import { UserService } from '~/services/user';

// Props 定义
interface Props {
    searchQuery: string;
}

const props = withDefaults(defineProps<Props>(), {
    searchQuery: ''
});

const knowledgeAssistantMemberInfo = computed(() => {
    return UserService.getKnowledgeAssistantMemberInfo()
})


// 内部状态管理
const page = reactive({
    current: 1,
    pageSize: 15,
    total: 0,
    pages: 1,
});

// 搜索结果和加载状态
const cholarList = ref<any[]>([]);
const loading = ref(false);

// 筛选条件
const filters = reactive({
    timeRange: '', // 时间范围，空字符串表示不限
    as_rr: 0, // 是否为评论性文章 (0: 不限, 1: 评论性文章)
    scisbd: 0, // (0:相关性,  1:日期)
    // as_vis: 1, // 是否包含引用 (0: 不包含, 1: 包含) - 默认包含引用
    // as_sdt: 0, // 是否包含专利 (0: 不包含, 7: 包含)
    as_ylo: 0, // 开始时间 - 默认为0表示不限
    as_yhi: 0, // 结束时间 - 默认为0表示不限
    includePatents: false, // 专利复选框状态
    includeCitations: true // 引用复选框状态 - 默认选中
});



// 学术搜索API调用
const getScholar = async (customQuery?: string) => {
    const queryToUse = customQuery || props.searchQuery;
    if (!queryToUse.trim()) return;

    loading.value = true;
    try {
        const params: any = {
            "query": queryToUse,
            "page": page.current,
        };

        // 添加筛选参数
        if (filters.as_rr !== 0) {
            params.as_rr = filters.as_rr;
        }

        if (filters.scisbd !== 0) {
            params.scisbd = filters.scisbd;
        }

        // if (filters.as_vis !== 0) {
        //     params.as_vis = filters.as_vis;
        // }


        // if (filters.as_sdt !== 0) {
        //     params.as_sdt = filters.as_sdt;
        // }

        // 处理时间范围
        if (filters.timeRange && filters.timeRange !== '') {
            if (filters.timeRange === 'custom') {
                // 自定义时间范围
                if (filters.as_ylo && filters.as_ylo !== 0) params.as_ylo = filters.as_ylo;
                if (filters.as_yhi && filters.as_yhi !== 0) params.as_yhi = filters.as_yhi;
            } else {
                // 预设时间范围（从某年开始到现在）
                params.as_ylo = filters.timeRange;
            }
        }

        const res = await scholarSearch(params);

        if (!res.ok) {
            return;
        }

        if (res.code == 7004) {
            message.warning(res.message || '学术搜索次数不足');
            return
        }

        // 如果 次数小于 10 次 就提示 一下
        if (knowledgeAssistantMemberInfo.value?.usedScholarSearch || 0 < 10) {
            message.warning(`剩余学术搜索：${knowledgeAssistantMemberInfo.value?.usedScholarSearch || 0}次`);
        }

        cholarList.value = res.data?.records || [];

        Object.assign(page, {
            current: res.data?.current,
            total: res.data?.total,
            pageSize: res.data?.size,
            pages: res.data?.pages,
        });
    } catch (error) {
        console.error('搜索失败:', error);
    } finally {
        loading.value = false;
    }
};

// 处理筛选条件变化
const handleFilterChange = () => {
    // 重置页码
    page.current = 1;
    // 重新搜索
    getScholar();
};



// 跳转到指定页面
const goToPage = (pageNum: number) => {
    if (pageNum >= 1 && pageNum <= page.pages && pageNum !== page.current) {
        page.current = pageNum;
        getScholar();
    }
};



// 执行搜索的方法（供父组件调用）
const performSearch = (query: string) => {
    if (query.trim()) {
        page.current = 1;
        getScholar(query);
    }
};

// 移除自动搜索的监听器，改为手动触发搜索

// 暴露给父组件的方法和数据
defineExpose({
    page,
    filters,
    performSearch,
    getScholar
});


</script>

<style scoped>
.custom-scrollbar::-webkit-scrollbar {
    width: 4px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 2px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}

/* Firefox */
.custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #d1d5db transparent;
}
</style>
