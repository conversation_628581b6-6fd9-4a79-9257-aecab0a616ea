<template>
    <!-- 右侧内容区域 -->
    <div class="w-4/5 overflow-y-auto px-4 pt-4 pb-[5%]">
        <!-- Loading状态 -->
        <div v-if="loading" class="flex items-center justify-center h-64">
            <div class="flex flex-col items-center space-y-4">
                <loading theme="outline" size="40" class="text-gray-400 animate-spin" />
                <div class="text-[#666] text-sm">正在搜索中...</div>
            </div>
        </div>

        <!-- 搜索结果 -->
        <div v-else class="space-y-8 flex justify-between flex-col">
            <!-- 空状态 -->
            <div v-if="cholarList.length == 0" class="flex items-center justify-center h-64">
                <div class="flex flex-col items-center space-y-4 text-center">
                    <div class="text-6xl text-gray-300">📚</div>
                    <div class="text-[#666] text-lg">暂无搜索结果</div>
                    <div class="text-[#999] text-sm">请尝试调整搜索关键词或筛选条件</div>
                </div>
            </div>

            <!-- 搜索结果列表 -->
            <div v-for="item in cholarList" :key="item.data_cid" class=" w-full flex items-center">
                <div class="w-3/5 space-y-2">
                    <!-- 标题高亮 -->
                    <a :href="item.main_url || item.pdf_url" target="_blank"
                        class="text-lg font-medium text-blue-700 hover:text-[#5F43BF]"
                        v-html="highlightText(item.title, searchQuery)"></a>

                    <!-- 作者、出版信息高亮 -->
                    <div class="text-sm text-[#6675A2]"
                        v-html="highlightText(`${item.authors} - ${item.publication}, ${item.year}`, searchQuery)">
                    </div>

                    <!-- 摘要高亮 -->
                    <div v-if="item.abstract" class="text-sm text-[#333] line-clamp-3"
                        v-html="highlightText(item.abstract, searchQuery)"></div>

                    <!-- 引用与 PDF -->
                    <div class="flex items-center space-x-4 text-sm text-gray-500">
                        <div v-if="item.citations">被引用 {{ item.citations }} 次</div>
                    </div>
                </div>
                <div class="w-2/5 flex justify-center">
                    <button @click="addToLibrary(item)" :disabled="uploadingItems.has(item.data_cid)"
                        class="flex items-center justify-center text-sm text-[#333333] px-4 py-2 border border-[#DEE6FD] rounded-lg space-x-1 hover:text-[#2551B5] hover:border-[#2551B5] disabled:opacity-50 disabled:cursor-not-allowed"
                        v-if="item.pdf_url">
                        <loading v-if="uploadingItems.has(item.data_cid)" theme="outline" size="16"
                            class="text-inherit animate-spin" :fill="'currentColor'" />
                        <lightning v-else theme="outline" size="16" :fill="'currentColor'" />
                        <span>{{ uploadingItems.has(item.data_cid) ? '添加中...' : '添加到知识库' }}</span>
                    </button>
                </div>
            </div>

            <!-- 分页器 -->
            <div v-if="page.pages > 1 && cholarList.length != 0"
                class="w-3/5 mt-4 sm:mt-6 pb-16 md:pb-0 flex justify-center">
                <div class="flex items-center space-x-1">
                    <!-- 上一页 -->
                    <button @click="goToPage(page.current - 1)" :disabled="page.current === 1"
                        class="px-3 py-2 text-sm  hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                        上一页
                    </button>

                    <!-- 页码 -->
                    <button v-for="pageNum in getVisiblePages()" :key="pageNum" @click="goToPage(pageNum)" :class="[
                        'px-3 py-2 text-sm rounded',
                        pageNum === page.current
                            ? 'text-[#2551B5]'
                            : 'hover:text-[#2551B5]'
                    ]">
                        {{ pageNum }}
                    </button>

                    <!-- 下一页 -->
                    <button @click="goToPage(page.current + 1)" :disabled="page.current === page.pages"
                        class="px-3 py-2 text-sm rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                        下一页
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { addFiles } from '@/api/repositoryFile.js';
import { uploadByUrl } from '@/api/upload';
import { BindPhoneModal, HTTP_STATUS } from '@/utils/constants';
import { Lightning, Loading } from '@icon-park/vue-next';
import { message } from 'ant-design-vue';
import { ref } from 'vue';
import { UserService } from '~/services/user';
import { useUserStore } from '~/stores/user';
import { removeQuestionMarkText } from '~/utils/utils';

// Props 定义
interface Props {
    loading: boolean;
    cholarList: any[];
    searchQuery: string;
    page: any;
}

const props = defineProps<Props>();

// Emits 定义
const emit = defineEmits<{
    goToPage: [pageNum: number];
}>();

// 跟踪正在上传的项目
const uploadingItems = ref(new Set<string>());

// 高亮文本函数
function highlightText(text: string, keyword: string): string {
    if (!keyword || !text) return text;
    const escapedKeyword = keyword.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&');
    const reg = new RegExp(escapedKeyword, 'gi');
    return text.replace(reg, match => `<span class="text-red-500">${match}</span>`);
}

// 跳转到指定页面
const goToPage = (pageNum: number) => {
    if (pageNum >= 1 && pageNum <= props.page.pages && pageNum !== props.page.current) {
        emit('goToPage', pageNum);
    }
};

// 获取可见的页码数组
const getVisiblePages = () => {
    const current = props.page.current;
    const total = props.page.pages;
    const visiblePages: number[] = [];

    // 最多显示10个页码
    const maxVisible = 10;

    if (total <= maxVisible) {
        // 如果总页数不超过10页，显示所有页码
        for (let i = 1; i <= total; i++) {
            visiblePages.push(i);
        }
    } else {
        // 如果总页数超过10页，智能显示页码
        let start = Math.max(1, current - 4);
        let end = Math.min(total, start + maxVisible - 1);

        // 调整起始位置，确保显示10个页码
        if (end - start + 1 < maxVisible) {
            start = Math.max(1, end - maxVisible + 1);
        }

        for (let i = start; i <= end; i++) {
            visiblePages.push(i);
        }
    }

    return visiblePages;
};



// 添加到知识库
const addToLibrary = async (item: any) => {
    console.log('添加到知识库:', item)

    // 检查用户是否已登录
    const userStore = useUserStore()
    if (!userStore.isLogined) {
        userStore.openLoginModal()
        return
    }

    if (!item.pdf_url) {
        message.error('该文献没有可用的PDF链接')
        return
    }

    // 防止重复上传
    if (uploadingItems.value.has(item.data_cid)) {
        return
    }

    try {
        // 添加到上传中的集合
        uploadingItems.value.add(item.data_cid)
        // 从PDF URL获取文件名
        const fileName = `${item.title.substring(0, 50)}.pdf` // 取标题前50个字符作为文件名

        const uploadByUrlParams = {
            fileName: fileName,
            fileUrl: removeQuestionMarkText(item.pdf_url),
        }

        // 保存文件信息到系统
        const uploadByUrlResult = await uploadByUrl(uploadByUrlParams)

        if (!uploadByUrlResult.ok) {
            throw new Error('文件保存失败')
        }

        // 添加到知识库
        const res = await addFiles({
            "spaceId": UserService.getSelfUserId(),
            "folderId": 0, // 添加到根目录，如果需要指定文件夹可以传入folderId
            fileIds: [uploadByUrlResult.data.id]
        })

        if (res?.code == HTTP_STATUS.MOBILE_NOT_BOUND) {
            const user = useUserStore()
            user.setShowPhoneBoundModal({
                status: BindPhoneModal.SHOW_BINDING,
            })
            return
        }

        if (!res || !res.ok) {
            throw new Error('添加到知识库失败')
        }

        message.success('PDF已成功添加到知识库')

    } catch (error: any) {
        console.error('添加到知识库失败:', error)
        message.error(error?.message || 'PDF添加失败，请重试')
    } finally {
        // 从上传中的集合移除
        uploadingItems.value.delete(item.data_cid)
    }
}


</script>
