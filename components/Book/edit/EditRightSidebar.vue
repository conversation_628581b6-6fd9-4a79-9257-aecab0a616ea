<!--
  右侧AI助手面板
  功能：
  1. 显示参考资料库
  2. 提供创作助手功能
  3. 监听editor:citationClick事件，自动显示参考资料库抽屉
-->
<template>
  <div class="h-full">
    <div class="flex flex-col h-full edit-right-sidebar">

      <!-- 参考资料库区域和操作区 -->
      <div class="text-gray-700 flex flex-col px-[20px] py-[20px] bg-[#F5F7FF] border-l border-solid border-[#ECF1FF]">

        <!-- 用于Tour的直接引用元素 -->
        <div ref="fileAreaRef" class="mb-2">
          <!-- 使用参考资料库组件 -->
          <ReferenceLibrary ref="referenceLibraryRef" v-if="chapterStore.bookValue?.key" />
        </div>

        <!-- 右侧功能操作区域 -->
        <div class="mt-[20px]  flex flex-col overflow-hidden" :ref="(el) => {
          if (el) creativeAssistantRef = el
        }">
          <div class="flex items-center justify-between text-sm text-gray-700 flex-shrink-0 mb-2 min-h-[24px]">
            <div class="flex items-center ">
              <div class="bg-[#D1F7F8] rounded-[6px] w-[25px] h-[25px] flex items-center justify-center">
                <BookIconfont name="a-bianzu144" :size="20" color="#20C3AB"></BookIconfont>
              </div>
              <span class="ml-2 font-bold text-[16px]">写作助手</span>
            </div>
          </div>

          <div class="flex-shrink-0">
            <!-- 用户输入的提示词、思路或关键词等区域 v-show="chapterStore.isShowCreativeAssistant"-->
            <div class="relative w-full bg-white border-1 border-[#E3EAFB] rounded-[10px] text-[#333333]">
              <a-textarea v-model:value="chapterStore.userEditCustomPrompt" :auto-size="{ minRows: 4, maxRows: 8 }"
                placeholder="请输入你的观点思路、辅助参考信息等，输入越清晰具体，AI越能准确理解你的写作需求"
                class="custom-textarea w-full rounded-lg p-2 sm:p-3 four text-[13px] bg-transparent border-transparent"
                :maxlength="2000" />

              <div class="flex justify-between w-full h-[30px]">
                <!-- <a-button size="small" @click="handlePressUploadFile">上传文件</a-button> -->
                <button @click="handlePressUploadFile"
                  class="flex items-center justify-start absolute left-1.5 bottom-1.5 px-[12px] py-[5px] rounded-[7px] text-xs text-[#2551B5] bg-[#E7EDFE]">
                  <plus theme="outline" size="16" />上传文件
                </button>
                <button @click="chapterStore.userEditCustomPrompt = ''"
                  class="absolute right-1.5 bottom-1.5 px-2 text-xs text-gray-400 hover:text-gray-600">
                  清空
                </button>
              </div>

            </div>

            <div class="my-2 w-full">
              <!-- 章节临时文件区域 -->
              <div v-for="item in chapterStore.bookUploadTemplateFileList" :key="item.id"
                class="flex items-center justify-between bg-[#ffffff] px-2 py-2 rounded-lg mt-1">
                <div class="flex items-center justify-start text-[14px] overflow-hidden flex-1">
                  <img :src="getFileIcon(getFileTypeFromName(item.name || item.fileName || ''))"
                    :alt="getFileTypeFromName(item.name || item.fileName || '')"
                    class="w-[18px] h-[18px] mr-1 flex-shrink-0">
                  <span class="truncate">{{ item.name || item.fileName }}</span>
                </div>
                <div class="p-2 cursor-pointer flex-shrink-0">
                  <close theme="outline" size="16" @click="handlePressDeleteFile(item)" />
                </div>
              </div>
            </div>

            <!-- 操作生成章节按钮区域 -->
            <div class="flex justify-between space-x-4 mt-4">
              <ContentGenerate :learningCount="referenceLibraryRef?.learningCount || 0" />
              <BulkGenerate :learningCount="referenceLibraryRef?.learningCount || 0" />
              <!-- <div class="flex items-center text-sm whitespace-nowrap cursor-pointer text-[#2551B5]"
                @click="toggleCreativeAssistant">
                <template v-if="chapterStore.isShowCreativeAssistant">
                  <collapse-text-input theme="outline" /><span class="ml-1">收起</span>
                </template>
<template v-else>
                  <expand-text-input theme="outline" /> <span class="ml-1">展开</span>
                </template>
</div> -->
            </div>
          </div>

          <div class="mt-[20px]">
            <div class="flex items-center ">
              <div class="bg-[#E3DFFF] rounded-[6px] w-[25px] h-[25px] flex items-center justify-center">
                <BookIconfont name="duomotai" :size="15" color="#7A4AE0"></BookIconfont>
              </div>
              <div class="ml-2">
                <span class="font-bold text-[16px]">AI多模态生成</span>
                <div class="text-[#999999] text-[13px] mt-[7px]">选中文字后，点击对应多模态生成工具</div>
              </div>
            </div>
          </div>
          <div class="flex justify-between mt-2 gap-2 flex-shrink-0">
            <div
              class="flex flex-col justify-center items-center bg-gradient-to-br from-[#e4f0ff] to-[#d1e4ff] rounded-[7px] flex-1 flex-shrink-0 p-[12px] text-[#5076e6] cursor-pointer text-[14px]"
              @click="handlePressInsertSvg">
              <Iconfont style="height: 42px;" name="shiyitu" :size="28" color="#5076e6"></Iconfont>
              <div>示意图</div>
            </div>
            <div
              class="flex flex-col justify-center items-center bg-gradient-to-br from-[#EBEFFF] to-[#CED7FC] rounded-[7px] flex-1 flex-shrink-0 p-[12px] text-[#7A4AE0] cursor-pointer text-[14px]"
              @click="handlePressInsertChart">
              <BookIconfont name="tubiao" :size="28" color="#7A4AE0"></BookIconfont>
              <div>图表</div>
            </div>
          </div>
          <div class="flex justify-between mt-2 gap-2 flex-shrink-0">
            <div
              class="flex flex-col justify-center items-center bg-gradient-to-br from-[#E3F6FF] to-[#C3E8F4] rounded-[7px] flex-1 flex-shrink-0 p-[12px] text-[#31A1C8] cursor-pointer text-[14px]"
              @click="handlePressInsertImg">
              <BookIconfont name="tupian" :size="28" color="#31A1C8"></BookIconfont>
              <div>图片</div>
            </div>
            <div
              class="flex flex-col justify-center items-center bg-gradient-to-br from-[#E3E5FF] to-[#D1D5FF] rounded-[7px] flex-1 flex-shrink-0 p-[12px] text-[#5076E6] cursor-pointer text-[14px]"
              @click="handlePressInsertTable">
              <BookIconfont name="biaoge1" :size="28" color="#5076E6"></BookIconfont>
              <div>表格</div>
            </div>
            <div
              class="flex flex-col justify-center items-center bg-gradient-to-br from-[#FFF3E8] to-[#FADFD2] rounded-[7px] flex-1 flex-shrink-0 p-[12px] text-[#E9824A] cursor-pointer text-[14px]"
              @click="handlePressInsertMath">
              <BookIconfont name="gongshi" :size="28" color="#E9824A"></BookIconfont>
              <div>公式</div>
            </div>
          </div>

          <div class="mt-[20px]">
            <div class="flex items-center ">
              <div class="bg-[#D7FBF3] rounded-[6px] w-[25px] h-[25px] flex items-center justify-center">
                <BookIconfont name="xueshubiaoti" :size="15" color="#1FA182"></BookIconfont>
              </div>
              <div class="ml-2">
                <span class="font-bold text-[16px]">学术优化</span>
              </div>
            </div>
          </div>
          <div class="flex justify-between mt-2 gap-2 flex-shrink-0">
            <div
              class="flex flex-col justify-center items-center bg-gradient-to-br from-[#E4F0FF] to-[#CFE3FE] rounded-[7px] flex-1 flex-shrink-0 p-[12px] text-[#337ED3] cursor-pointer text-[14px]"
              @click="handleAcademicSearch">
              <BookIconfont name="xueshusousuo" :size="28" color="#337ED3"></BookIconfont>
              <div>学术搜索</div>
            </div>
            <!-- <div
              class="flex flex-col justify-center items-center bg-gradient-to-br from-[#FFF8E3] to-[#FAE1B8] rounded-[7px] flex-1 flex-shrink-0 p-[12px] text-[#D08529] cursor-pointer text-[14px]"
              @click="handleAcademicFormat">
              <BookIconfont name="wenxiangeshi" :size="28" color="#D08529"></BookIconfont>
              <div>文献格式</div>
            </div> -->
            <!-- <div
              class="flex flex-col justify-center items-center bg-gradient-to-br from-[#DAFFF1] to-[#C3F4E4] rounded-[7px] flex-1 flex-shrink-0 p-[12px] text-[#1FA182] cursor-pointer text-[14px]"
              @click="handleChartSort">
              <BookIconfont name="tubiaopaixu" :size="28" color="#1FA182"></BookIconfont>
              <div>图表排序</div>
            </div> -->
          </div>
        </div>
      </div>


      <!-- 批量生成进度条区域 -->
      <div class="tubiao px-8" v-if="taskStore.isTraversing || taskStore.traverseProgress >= 100">
        <a-progress :percent="taskStore.traverseProgress" />
      </div>

      <!-- 历史消息区域 -->
      <div class="flex-1 flex overflow-hidden min-h-[300px]">
        <HistoryMessages />
      </div>

    </div>

    <img-generate-modal v-if="chapterStore.showImgModal" v-model="chapterStore.showImgModal"
      @confirm="chapterStore.insertImgContent" @update:model-value="(val) => !val && resetSelection()" />

    <table-generate-modal v-if="chapterStore.showTableModal" v-model="chapterStore.showTableModal"
      @confirm="chapterStore.insertTableContent" @update:model-value="(val) => !val && resetSelection()" />

    <chart-generate-modal v-if="chapterStore.showChartModal" v-model="chapterStore.showChartModal"
      @confirm="chapterStore.insertChartContent" @update:model-value="(val) => !val && resetSelection()" />

    <math-generate-modal v-if="chapterStore.showMathModal" v-model="chapterStore.showMathModal"
      @confirm="chapterStore.insertMathContent" @update:model-value="(val) => !val && resetSelection()" />

    <svg-generate-modal v-if="chapterStore.showSvgModal" v-model="chapterStore.showSvgModal"
      @confirm="onOpenSvgEditModal" @update:model-value="(val: any) => !val && resetSelection()" />

    <file-add-modal
      v-if="chapterStore.showFileAddModal && chapterStore.chapterUploadFileModal == BookUploadFileType.bookTemplate"
      v-model:model-value="chapterStore.showFileAddModal" :uploadFileCount="canUploadFileCount"
      @confirm="onConfirmFileAdd" />

    <academic-format-modal v-if="showAcademicFormatModal" v-model="showAcademicFormatModal"
      @confirm="onAcademicFormatConfirm" @update:model-value="(val) => !val && resetSelection()" />

    <chart-sort-modal v-if="showChartSortModal" v-model="showChartSortModal" @confirm="onChartSortConfirm"
      @update:model-value="(val) => !val && resetSelection()" />

    <academic-search-modal v-if="showAcademicSearchModal" v-model="showAcademicSearchModal"
      @confirm="onAcademicSearchConfirm" @update:model-value="(val) => !val && resetSelection()" />
  </div>
</template>

<script setup lang="ts">
import BookIconfont from '@/components/Book/BookIconfont.vue';
import AcademicFormatModal from '@/components/Book/common/AcademicFormatModal.vue';
import AcademicSearchModal from '@/components/Book/common/AcademicSearchModal.vue';
import ChartGenerateModal from '@/components/Book/common/ChartGenerateModal.vue';
import ChartSortModal from '@/components/Book/common/ChartSortModal.vue';
import FileAddModal from '@/components/Book/common/FileAddModal.vue';
import ImgGenerateModal from '@/components/Book/common/ImgGenerateModal.vue';
import MathGenerateModal from '@/components/Book/common/MathGenerateModal.vue';
import ReferenceLibrary from '@/components/Book/common/ReferenceLibrary.vue';
import SvgGenerateModal from '@/components/Book/common/SvgGenerateModal.vue';
import TableGenerateModal from '@/components/Book/common/TableGenerateModal.vue';
import { useChapterStore } from '@/stores/chapter';
import { useSchematicSvgStore } from '@/stores/schematicSvgStore';
import { BOOK_PAY_TRIGGER_TYPE, BookUploadFileType, KnowledgeFileIcon, MAX_REFERENCE_TEMPLATE_FILE_COUNT } from '@/utils/constants';
import { Close, Plus } from '@icon-park/vue-next';
import { message } from 'ant-design-vue';
import { storeToRefs } from 'pinia';
import { defineExpose, onMounted, ref } from 'vue';
import { useBookEditorStore } from '~/stores/bookEditor';
import { useTaskStore } from '~/stores/task';
import BulkGenerate from './BulkGenerate.vue';
import ContentGenerate from './ContentGenerate.vue';
import HistoryMessages from './HistoryMessages.vue';

const { editor } = storeToRefs(useBookEditorStore())
const referenceLibraryRef = ref()
const creativeAssistantRef = ref()
const fileAreaRef = ref()
const chapterStore = useChapterStore()
const taskStore = useTaskStore()
const { isGenerating, isTraversing } = storeToRefs(taskStore)


const canUploadFileCount = ref<number>(0)

// 学术优化功能的模态状态
const showAcademicFormatModal = ref(false)
const showChartSortModal = ref(false)
const showAcademicSearchModal = ref(false)

const svgStore = useSchematicSvgStore()

// 监听编辑器的选择改变事件
onMounted(() => {

});

// 当弹窗关闭时重置选择状态
const resetSelection = () => {
  // hasRealSelection.value = false;
  clearEditorSelection()
};

const clearEditorSelection = () => {
  try {
    // 1. 清除浏览器DOM选区
    const selection = window.getSelection();
    if (selection) {
      selection.removeAllRanges();
    }

    // 2. 清除编辑器内部选区
    if (editor?.value) {
      // 获取当前文档的结尾位置
      const docEnd = editor.value.state.doc.content.size;

      // 创建一个新的光标位置（不是选区范围）
      // 使用简单的方法设置光标位置到文档末尾
      editor.value.commands.setTextSelection(docEnd);

      // 强制编辑器更新视图
      editor.value.view.updateState(editor.value.view.state);

      // 确保获得焦点
      // editor.value.commands.focus();
    }

    // 3. 重置选区状态变量
    // hasRealSelection.value = false;

    console.log('选区已完全清空');
  } catch (error) {
    console.error('清空选区时出错:', error);
  }
};

const getEditorSelectedText = () => {
  if (!editor || !editor.value) return null

  const selection = editor.value.state?.selection
  if (!selection) return null

  const from = selection.from
  const to = selection.to
  console.log("selectedText from ==>", from, to)

  // 检查是否是空选择（只有光标位置）
  if (from === to) {
    console.log("编辑器选区起点和终点相同，返回null");
    return null
  }

  // 获取选中的文本
  const selectedText = editor.value.view?.state?.doc?.textBetween(from, to)

  // 检查选中的文本是否为空
  if (!selectedText || selectedText.trim().length === 0) {
    console.log("选中文本为空，返回null");
    return null
  }

  // 检查是否是有效的选择 - 选择长度必须大于3个字符才被视为有效选择
  if (to - from < 3 || selectedText.trim().length === 0) {
    console.log("选中文本长度小于3，返回null");
    return null
  }

  // 保存选中文本到referceText，如果长度大于10
  // if (selectedText.length > 10) {
  chapterStore.referceText = selectedText
  // }

  // 修复：启用编辑器chain命令时确保焦点（但不影响选区）
  // editor.value.chain().focus();

  console.log("最终返回的选中文本 ==>", selectedText)
  return selectedText
}

const checkActionPermission = () => {
  if (referenceLibraryRef.value?.learningCount > 0) {
    message.warning('资料库中所有文件学习完成后才可以开始生成')
    return false
  }
  if (taskStore.isTraversing) {
    message.warning('正在批量生成章节内容，请等待完成后再切换章节')
    return false
  }

  if (taskStore.isGenerating) {
    message.warning('正在生成章节内容，请等待完成后再切换章节')
    return false
  }

  if (!chapterStore.currentChapter) {
    message.warning('请先选择一个章节')
    return false
  }
  return true
}

const onOpenSvgEditModal = () => {

}

const handlePressInsertImg = () => {
  if (!checkActionPermission()) {
    return
  }
  getEditorSelectedText()
  chapterStore.payTriggerType = BOOK_PAY_TRIGGER_TYPE.GENERATE_IMG
  chapterStore.showImgModal = true
  // 获取完选中内容后清空选区
  // clearEditorSelection()
}

const handlePressInsertTable = () => {
  if (!checkActionPermission()) {
    return
  }
  getEditorSelectedText()
  chapterStore.payTriggerType = BOOK_PAY_TRIGGER_TYPE.GENERATE_TABLE
  chapterStore.showTableModal = true
  // 获取完选中内容后清空选区
  // clearEditorSelection()
}

const handlePressInsertSvg = async () => {
  if (!checkActionPermission()) {
    return
  }
  getEditorSelectedText()

  setSchematicReferceText()
  chapterStore.payTriggerType = BOOK_PAY_TRIGGER_TYPE.GENERATE_SVG
  chapterStore.showSvgModal = true

  // clearEditorSelection()
}

const handlePressInsertChart = () => {
  if (!checkActionPermission()) {
    return
  }
  getEditorSelectedText()
  chapterStore.payTriggerType = BOOK_PAY_TRIGGER_TYPE.GENERATE_CHAT
  chapterStore.showChartModal = true
  // 获取完选中内容后清空选区
  // clearEditorSelection()
}

const handlePressInsertMath = () => {
  if (!checkActionPermission()) {
    return
  }
  getEditorSelectedText()
  chapterStore.payTriggerType = BOOK_PAY_TRIGGER_TYPE.GENERATE_MATH
  chapterStore.showMathModal = true
  // 获取完选中内容后清空选区
  // clearEditorSelection()
}

const setSchematicReferceText = () => {
  if (!editor.value || !editor.value.state || !editor.value.state.selection) {
    chapterStore.referceText = ''
    return
  }

  const { from, to } = editor.value?.state?.selection
  const selectedText = editor.value?.view.state.doc.textBetween(from, to)

  // 获取选中文本所在的完整段落
  let currentParagraphText = ''
  let beforeParagraphText = ''
  let afterParagraphText = ''

  // 存储查找到的段落节点信息
  const paragraphs: any[] = []
  let currentParagraphIndex = -1

  // 遍历文档获取所有段落
  editor.value.state.doc.descendants((node: any, pos: any) => {
    // 只处理段落和标题节点
    if (node.type.name === 'paragraph' || node.type.name === 'heading') {
      const paragraphFrom = pos
      const paragraphTo = pos + node.nodeSize

      // 保存段落信息
      paragraphs.push({
        node,
        from: paragraphFrom,
        to: paragraphTo,
        text: editor.value?.view.state.doc.textBetween(paragraphFrom, paragraphTo)
      })

      // 如果选区在此段落内，记录当前段落索引
      if (from >= paragraphFrom && to <= paragraphTo) {
        currentParagraphIndex = paragraphs.length - 1
        // 获取完整的段落文本
        currentParagraphText = editor.value?.view.state.doc.textBetween(paragraphFrom, paragraphTo) || ''
      }
    }
    return true // 继续遍历
  })

  // 获取上一段和下一段
  if (currentParagraphIndex > 0) {
    beforeParagraphText = paragraphs[currentParagraphIndex - 1].text
  }

  if (currentParagraphIndex >= 0 && currentParagraphIndex < paragraphs.length - 1) {
    afterParagraphText = paragraphs[currentParagraphIndex + 1].text
  }

  // 当选择的是段落的部分内容时，使用完整段落文本
  if (
    currentParagraphText &&
    selectedText !== currentParagraphText &&
    selectedText.length < currentParagraphText.length
  ) {
    chapterStore.schematicReferceText = currentParagraphText
  } else {
    chapterStore.referceText = selectedText || ''
  }

  // 设置上下文文本
  chapterStore.referceBeforeText = beforeParagraphText || ''
  chapterStore.referceAfterText = afterParagraphText || ''
}

// 根据文件类型获取对应的图标名
const getFileIcon = (fileType: string): string => {
  switch (fileType.toLowerCase()) {
    case 'pdf':
      return KnowledgeFileIcon.pdf;
    case 'doc':
    case 'docx':
      return KnowledgeFileIcon.doc;
    case 'ppt':
    case 'pptx':
      return KnowledgeFileIcon.ppt;
    case 'img':
    case 'jpg':
    case 'jpeg':
    case 'png':
      return KnowledgeFileIcon.img;
    case 'txt':
    case 'md':
    case 'text':
      return KnowledgeFileIcon.text;
    case 'xlsx':
    case 'csv':
      return KnowledgeFileIcon.xlsx;
    default:
      return KnowledgeFileIcon.encode;
  }
}

// 根据文件名获取文件类型
const getFileTypeFromName = (fileName: string): string => {
  if (!fileName) return ''

  const extension = fileName.split('.').pop()?.toLowerCase() || ''

  // 返回文件扩展名作为类型
  return extension
}


const handlePressDeleteFile = (item: any) => {
  chapterStore.bookUploadTemplateFileList = chapterStore.bookUploadTemplateFileList.filter(child => child.id != item.id)
}

const handlePressUploadFile = () => {
  if (isGenerating.value) {
    message.warning('正在生成章节内容，请等待完成后再上传文件')
    return
  }
  if (isTraversing.value) {
    message.warning('正在批量生成章节内容，请等待完成后再上传文件')
    return
  }
  if (chapterStore.bookUploadTemplateFileList.length >= MAX_REFERENCE_TEMPLATE_FILE_COUNT) {
    message.warning('章节最多上传3个文件')
    return
  }

  canUploadFileCount.value = MAX_REFERENCE_TEMPLATE_FILE_COUNT - chapterStore.bookUploadTemplateFileList.length

  chapterStore.chapterUploadFileModal = BookUploadFileType.bookTemplate
  chapterStore.showFileAddModal = true

}

// 确认添加文件
const onConfirmFileAdd = async (_list: any[], type: BookUploadFileType) => {
  if (_list.length == 0) {
    return
  }
  if (type != BookUploadFileType.bookTemplate) {
    return
  }
  chapterStore.bookUploadTemplateFileList.push(..._list)
}

// 学术搜索点击事件
const handleAcademicSearch = () => {
  if (!checkActionPermission()) {
    return
  }
  const content = getEditorSelectedText()
  if (!content || content.trim().length == 0) {
    message.warning('请先选中需要插入引用内容的文本')
    return
  }
  chapterStore.payTriggerType = BOOK_PAY_TRIGGER_TYPE.ACADEMIC_SEARCH
  showAcademicSearchModal.value = true
}

// 学术格式点击事件
// const handleAcademicFormat = () => {
//   if (!checkActionPermission()) {
//     return
//   }
//   getEditorSelectedText()
//   chapterStore.payTriggerType = BOOK_PAY_TRIGGER_TYPE.ACADEMIC_FORMAT
//   showAcademicFormatModal.value = true
// }

// 一键图表排序点击事件
// const handleChartSort = () => {
//   if (!checkActionPermission()) {
//     return
//   }
//   getEditorSelectedText()
//   chapterStore.payTriggerType = BOOK_PAY_TRIGGER_TYPE.CHART_SORT
//   showChartSortModal.value = true
// }

// 学术格式确认回调
const onAcademicFormatConfirm = (data: { format: string, description: string }) => {

  // 这里将来会处理实际的格式应用逻辑
  message.success(`已设置为${data.format}格式`)
}

// 图表排序确认回调
const onChartSortConfirm = (data: { sortType: string, customDescription?: string, additionalRequirements?: string }) => {
  console.log('图表排序设置:', data)
  // 这里将来会处理实际的排序逻辑
  message.success(`已设置为${data.sortType}排序`)
}

// 学术搜索确认回调
const onAcademicSearchConfirm = (data: { action: string, includeReferences: boolean, selectedPaper?: number }) => {
  console.log('学术搜索操作:', data)

  // 构建插入的学术内容
  let insertContent = '\n\n'

  if (data.action === 'insert') {
    insertContent += '### 学术内容插入\n\n'

    if (data.selectedPaper) {
      insertContent += `基于选中的学术文献 ${data.selectedPaper}，相关研究表明：\n\n`
    }

    if (data.includeReferences) {
      insertContent += '**参考文献：**\n\n'
      insertContent += '1. 科方超.教育数字化转型背景下师范生数字素养评测研究[J]. 《科教导刊》,2024,(15):153-155.\n\n'
      insertContent += '2. 黄璐.我国体育公共服务数字化转型的成效、问题及对策研究[J] 《南京体育学院学报》,2025,(2):16-20.\n\n'
    }
  } else if (data.action === 'transform') {
    insertContent += '### 文本润色结果\n\n'
    insertContent += '结合学术研究成果，对原文进行了专业化表达和学术优化处理。\n\n'

    if (data.includeReferences) {
      insertContent += '**相关学术支撑：**\n\n'
      insertContent += '- 数字素养实践层面的技术应用能力构成\n'
      insertContent += '- 数字参与程度与素养发展的正相关关系\n\n'
    }
  }

  // 插入到编辑器中
  if (editor?.value) {
    const { state } = editor.value
    const { selection } = state
    const { to } = selection

    // 在选中文本后插入内容
    const insertPos = to

    editor.value.chain()
      .focus()
      .setTextSelection(insertPos)
      .insertContent(insertContent)
      .run()
  }

  // 清空选中文本状态
  chapterStore.referceText = ''

  const actionText = data.action === 'insert' ? '插入' : '转换'
  message.success(`已完成学术内容${actionText}`)
}

// 暴露ref给父组件
defineExpose({
  referenceLibraryRef,
  creativeAssistantRef,
  fileAreaRef
});
</script>

<style scoped>
.btn-with-icon {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.btn-with-icon :deep(.anticon) {
  display: flex !important;
  align-items: center !important;
  margin-right: 4px;
}

/* 自定义textarea样式，移除获取焦点时的边框颜色 */
:deep(.custom-textarea) {
  box-shadow: none !important;
  border: none !important;
}

:deep(.custom-textarea.ant-input:focus),
:deep(.custom-textarea.ant-input-focused) {
  border-color: transparent !important;
  box-shadow: none !important;
  outline: none !important;
}

:deep(.custom-textarea.ant-input:hover) {
  border-color: transparent !important;
}
</style>
