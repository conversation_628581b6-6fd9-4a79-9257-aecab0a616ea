<template>
  <a-modal v-model:open="localVisible" :width="600" :centered="true" :destroyOnClose="true" @cancel="handleCancel"
    class="exchange-search-modal">
    <div class="exchange-content">
      <!-- 选择区域 -->
      <div class="mb-6">
        <div class="text-[20px] text-center text-[#333333] mb-5 font-bold">选择兑换次数</div>
        <div class="flex gap-3 mb-4">
          <!-- 预设选项 -->
          <!-- <button v-for="(option, index) in EXCHANGE_OPTIONS" :key="index" @click="selectOption(index)" :class="[
            'p-3 border-[1px] rounded-lg text-center transition-all',
            selectedOptionIndex === index
              ? 'border-[#2551B5] bg-[#E7EDFE] text-[#2551B5]'
              : 'border-[#E5E5E5] hover:border-gray-300'
          ]">
            <div class="font-medium">{{ option.label }}</div>
            <div class="text-xs text-[#777777] mt-1">{{ formatCoins(option.coins) }}硬币</div>
          </button> -->
          <a-radio-group v-model:value="selectedOptionIndex">
            <a-radio-button :value="10">10次</a-radio-button>
            <a-radio-button :value="50">50次</a-radio-button>
            <a-radio-button :value="100">100次</a-radio-button>
          </a-radio-group>
          <!-- 自定义选项 -->
          <!-- <button @click="selectOption(3)" :class="[
            'p-3 border-[1px] rounded-lg text-center transition-all',
            selectedOptionIndex === 3
              ? 'border-[#2551B5] bg-[#E7EDFE] text-[#2551B5]'
              : 'border-[#E5E5E5] hover:border-gray-300'
          ]">
            <div class="font-medium">自定义</div>
            <div class="text-xs text-[#777777] mt-1">灵活选择</div>
          </button> -->
          <a-input-number v-model:value="customTimes" :min="1" :max="99999999" :step="1" placeholder="自定义"
            class="w-[]" @change="handleCustomTimesChange" />
        </div>

        <!-- 自定义输入框 -->
        <!-- <div v-if="selectedOptionIndex === 3" class="mt-3">
          <a-input-number v-model:value="customTimes" :min="1" :max="99999999" :step="1" placeholder="请输入次数"
            class="w-full" @change="handleCustomTimesChange" />
        </div> -->
      </div>

      <!-- 信息显示区域 -->
      <div class="bg-[#F7F7F7] rounded-lg p-4 space-y-3">
        <div class="flex justify-between items-center">
          <span class="text-sm text-[#777777]">消耗：</span>
          <span class="text-sm font-medium text-[#2551B5]">{{ formatCoins(requiredCoins) }}硬币</span>
        </div>
      </div>
      <div class="bg-[#F7F7F7] rounded-lg p-4 space-y-3 m-t-[20px]">

        <div class="flex justify-between items-center">
          <span class="text-sm text-[#777777]">剩余硬币：</span>
          <span class="text-sm font-medium" :class="isInsufficientCoins ? 'text-red-600' : 'text-[#333333]'">
            {{ formatCoins(userCoinBalance) }}
          </span>
        </div>
        <div v-if="isInsufficientCoins" class="text-xs text-red-600 bg-red-50 p-2 rounded">
          硬币余额不足，请先<a class="text-[#2551B5]" @click="handleRecharge">充值</a>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-end space-x-3">
        <a-button @click="handleCancel" :disabled="isExchanging">
          取消
        </a-button>
        <a-button type="primary" @click="handleConfirm" :loading="isExchanging"
          :disabled="isInsufficientCoins || selectedTimes <= 0" style="color: #ffffff;">
          确认
        </a-button>
      </div>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia';
import { computed, watch } from 'vue';
import { useExchangeSearchStore } from '~/stores/exchangeSearch';

const props = defineProps<{
  visible: boolean
}>()

const emit = defineEmits<{
  'update:visible': [value: boolean]
}>()

const exchangeStore = useExchangeSearchStore()
const {
  selectedOptionIndex,
  customTimes,
  isExchanging,
  selectedTimes,
  requiredCoins,
  userCoinBalance,
  isInsufficientCoins,
  remainingCoins
} = storeToRefs(exchangeStore)

// 本地可见性状态，用于双向绑定
const localVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 防止背景滚动
const preventBodyScroll = () => {
  document.body.style.overflow = 'hidden'
  document.body.style.paddingRight = '0px'
}

const restoreBodyScroll = () => {
  document.body.style.overflow = ''
  document.body.style.paddingRight = ''
}

const handleRecharge = () => {
  const rechargeStore = useRechargeStore()
  rechargeStore.openRechargeModal(RechargeModalTab.coin)
}

// 监听外部visible变化，同步到store
watch(() => props.visible, (newValue) => {
  if (newValue) {
    exchangeStore.openExchangeModal()
    preventBodyScroll()
  } else {
    exchangeStore.closeExchangeModal()
    restoreBodyScroll()
  }
})

// 组件卸载时恢复滚动
onUnmounted(() => {
  restoreBodyScroll()
})

// 选择选项
const selectOption = (index: number) => {
  exchangeStore.selectOption(index)
}

// 处理自定义次数变化
const handleCustomTimesChange = (value: string | number | null) => {
  if (value !== null && typeof value === 'number') {
    exchangeStore.setCustomTimes(value)
  }
}

// 格式化硬币数量显示
const formatCoins = (coins: number): string => {
  if (coins >= 10000) {
    return `${(coins / 10000).toFixed(1)}万`
  }
  return coins.toString()
}

// 取消操作
const handleCancel = () => {
  emit('update:visible', false)
}

// 确认兑换
const handleConfirm = async () => {
  await exchangeStore.performExchange()
  if (!exchangeStore.isExchanging) {
    emit('update:visible', false)
  }
}
</script>

<style scoped>
.exchange-search-modal :deep(.ant-modal-body) {
  padding: 24px;
}

.exchange-search-modal :deep(.ant-modal-footer) {
  padding: 16px 24px;
  border-top: 1px solid #f0f0f0;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .exchange-search-modal :deep(.ant-modal) {
    margin: 16px;
    max-width: calc(100vw - 32px);
  }

  .exchange-search-modal :deep(.ant-modal-body) {
    padding: 16px;
  }

  .exchange-search-modal :deep(.ant-modal-footer) {
    padding: 12px 16px;
  }
}
</style>
